{"_from": "babel-plugin-polyfill-corejs2@^0.4.10", "_id": "babel-plugin-polyfill-corejs2@0.4.13", "_inBundle": false, "_integrity": "sha512-3sX/eOms8kd3q2KZ6DAhKPc0dgm525Gqq5NtWKZ7QYYZEv57OQ54KtblzJzH1lQF/eQxO8KjWGIK9IPUJNus5g==", "_location": "/babel-plugin-polyfill-corejs2", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "babel-plugin-polyfill-corejs2@^0.4.10", "name": "babel-plugin-polyfill-corejs2", "escapedName": "babel-plugin-polyfill-corejs2", "rawSpec": "^0.4.10", "saveSpec": null, "fetchSpec": "^0.4.10"}, "_requiredBy": ["/@babel/plugin-transform-runtime", "/@babel/preset-env"], "_resolved": "https://registry.npmjs.org/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.4.13.tgz", "_shasum": "7d445f0e0607ebc8fb6b01d7e8fb02069b91dd8b", "_spec": "babel-plugin-polyfill-corejs2@^0.4.10", "_where": "C:\\Users\\<USER>\\OneDrive\\Documents\\Work\\dev\\tombola20\\tombola-app-v2\\frontend\\node_modules\\@babel\\preset-env", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "bundleDependencies": false, "dependencies": {"@babel/compat-data": "^7.22.6", "@babel/helper-define-polyfill-provider": "^0.6.4", "semver": "^6.3.1"}, "deprecated": false, "description": "A Babel plugin to inject imports to core-js@2 polyfills", "devDependencies": {"@babel/core": "^7.22.6", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/plugin-transform-for-of": "^7.22.5", "@babel/plugin-transform-modules-commonjs": "^7.22.5"}, "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "d87c29c909148920ad18690b63d450c561842298", "homepage": "https://github.com/babel/babel-polyfills#readme", "keywords": ["babel-plugin"], "license": "MIT", "main": "lib/index.js", "name": "babel-plugin-polyfill-corejs2", "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel-polyfills.git", "directory": "packages/babel-plugin-polyfill-corejs2"}, "version": "0.4.13"}