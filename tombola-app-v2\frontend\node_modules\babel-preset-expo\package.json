{"name": "babel-preset-expo", "version": "13.2.1", "description": "The Babel preset for Expo projects", "main": "build/index.js", "files": ["build", "lazy-imports-blacklist.js"], "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "prepare": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "repository": {"type": "git", "url": "git+https://github.com/expo/expo.git", "directory": "packages/babel-preset-expo"}, "keywords": ["babel", "babel-preset", "expo", "expo-web", "react-native", "react-native-web", "metro", "webpack"], "author": "Expo <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/expo/expo/issues"}, "homepage": "https://github.com/expo/expo/tree/main/packages/babel-preset-expo#readme", "eslintConfig": {"extends": "universe/node"}, "dependencies": {"@babel/helper-module-imports": "^7.25.9", "@babel/plugin-proposal-decorators": "^7.12.9", "@babel/plugin-syntax-export-default-from": "^7.24.7", "@babel/plugin-proposal-export-default-from": "^7.24.7", "@babel/plugin-transform-export-namespace-from": "^7.25.9", "@babel/plugin-transform-flow-strip-types": "^7.25.2", "@babel/plugin-transform-private-methods": "^7.24.7", "@babel/plugin-transform-private-property-in-object": "^7.24.7", "@babel/plugin-transform-runtime": "^7.24.7", "@babel/plugin-transform-modules-commonjs": "^7.24.8", "@babel/plugin-transform-object-rest-spread": "^7.24.7", "@babel/plugin-transform-parameters": "^7.24.7", "@babel/preset-react": "^7.22.15", "@babel/preset-typescript": "^7.23.0", "@react-native/babel-preset": "0.79.4", "babel-plugin-react-native-web": "~0.19.13", "babel-plugin-transform-flow-enums": "^0.0.2", "babel-plugin-syntax-hermes-parser": "^0.25.1", "debug": "^4.3.4", "react-refresh": "^0.14.2", "resolve-from": "^5.0.0"}, "peerDependencies": {"babel-plugin-react-compiler": "^19.0.0-beta-e993439-20250405"}, "peerDependenciesMeta": {"babel-plugin-react-compiler": {"optional": true}}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/traverse": "^7.9.0", "@babel/types": "^7.9.0", "babel-plugin-react-compiler": "^19.0.0-beta-e993439-20250405", "expo-module-scripts": "^4.1.7", "jest": "^29.2.1"}, "gitHead": "cc3b641cc2e4e7686dca75e7029cf76a07b3d647"}