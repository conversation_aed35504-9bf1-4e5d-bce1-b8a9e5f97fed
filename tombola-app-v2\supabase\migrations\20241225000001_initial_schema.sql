-- Tombola V2 Initial Schema Migration
-- Based on V1 schema with improvements and TypeScript compatibility

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create custom types
CREATE TYPE user_role AS ENUM ('user', 'admin', 'moderator');
CREATE TYPE product_status AS ENUM ('active', 'completed', 'cancelled');
CREATE TYPE payment_status AS ENUM ('pending', 'completed', 'failed', 'refunded');

-- Users table (extends auth.users)
CREATE TABLE "public"."user" (
    "id" uuid NOT NULL DEFAULT auth.uid(),
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "updated_at" timestamp with time zone NOT NULL DEFAULT now(),
    "username" character varying(50) UNIQUE,
    "email" character varying(255),
    "profile_picture" character varying(500),
    "bio" text,
    "role" user_role NOT NULL DEFAULT 'user',
    "is_active" boolean NOT NULL DEFAULT true,
    "last_login" timestamp with time zone
);

ALTER TABLE "public"."user" ENABLE ROW LEVEL SECURITY;

-- Categories table
CREATE TABLE "public"."category" (
    "id" uuid NOT NULL DEFAULT gen_random_uuid(),
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "updated_at" timestamp with time zone NOT NULL DEFAULT now(),
    "name" character varying(100) NOT NULL,
    "description" text,
    "is_active" boolean NOT NULL DEFAULT true,
    "sort_order" integer DEFAULT 0
);

ALTER TABLE "public"."category" ENABLE ROW LEVEL SECURITY;

-- Subcategories table
CREATE TABLE "public"."subcategory" (
    "id" uuid NOT NULL DEFAULT gen_random_uuid(),
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "updated_at" timestamp with time zone NOT NULL DEFAULT now(),
    "category_id" uuid NOT NULL,
    "name" character varying(100) NOT NULL,
    "description" text,
    "is_active" boolean NOT NULL DEFAULT true,
    "sort_order" integer DEFAULT 0
);

ALTER TABLE "public"."subcategory" ENABLE ROW LEVEL SECURITY;

-- Products table
CREATE TABLE "public"."product" (
    "id" uuid NOT NULL DEFAULT gen_random_uuid(),
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "updated_at" timestamp with time zone NOT NULL DEFAULT now(),
    "user_id" uuid NOT NULL,
    "category_id" uuid,
    "subcategory_id" uuid,
    "product_name" character varying(255) NOT NULL,
    "description" text,
    "price" decimal(10,2) NOT NULL,
    "ticket_cost" decimal(10,2) NOT NULL,
    "ticket_count" integer NOT NULL,
    "tickets_sold" integer NOT NULL DEFAULT 0,
    "product_images" text[],
    "status" product_status NOT NULL DEFAULT 'active',
    "raffle_drawn" boolean NOT NULL DEFAULT false,
    "product_sent" boolean NOT NULL DEFAULT false,
    "product_delivered" boolean NOT NULL DEFAULT false,
    "winner_id" uuid,
    "draw_date" timestamp with time zone,
    "fulfillment_method" character varying(50) DEFAULT 'shipping'
);

ALTER TABLE "public"."product" ENABLE ROW LEVEL SECURITY;

-- Tickets table
CREATE TABLE "public"."ticket" (
    "id" uuid NOT NULL DEFAULT gen_random_uuid(),
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "purchase_date" timestamp with time zone NOT NULL DEFAULT now(),
    "user_id" uuid NOT NULL,
    "product_id" uuid NOT NULL,
    "ticket_number" integer NOT NULL,
    "is_winner" boolean NOT NULL DEFAULT false
);

ALTER TABLE "public"."ticket" ENABLE ROW LEVEL SECURITY;

-- Payments table
CREATE TABLE "public"."payment" (
    "id" uuid NOT NULL DEFAULT gen_random_uuid(),
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "updated_at" timestamp with time zone NOT NULL DEFAULT now(),
    "user_id" uuid NOT NULL,
    "product_id" uuid NOT NULL,
    "ticket_ids" uuid[] NOT NULL,
    "amount" decimal(10,2) NOT NULL,
    "currency" character varying(3) NOT NULL DEFAULT 'GBP',
    "status" payment_status NOT NULL DEFAULT 'pending',
    "stripe_payment_intent_id" character varying(255),
    "stripe_charge_id" character varying(255),
    "payment_method" character varying(50),
    "processed_at" timestamp with time zone
);

ALTER TABLE "public"."payment" ENABLE ROW LEVEL SECURITY;

-- Comments table
CREATE TABLE "public"."comment" (
    "id" uuid NOT NULL DEFAULT gen_random_uuid(),
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "updated_at" timestamp with time zone NOT NULL DEFAULT now(),
    "content" text NOT NULL,
    "user_id" uuid NOT NULL,
    "product_id" uuid NOT NULL,
    "is_active" boolean NOT NULL DEFAULT true
);

ALTER TABLE "public"."comment" ENABLE ROW LEVEL SECURITY;

-- Follows table
CREATE TABLE "public"."follow" (
    "id" uuid NOT NULL DEFAULT gen_random_uuid(),
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "follower_id" uuid NOT NULL,
    "followee_id" uuid NOT NULL
);

ALTER TABLE "public"."follow" ENABLE ROW LEVEL SECURITY;

-- Notifications table
CREATE TABLE "public"."notification" (
    "id" uuid NOT NULL DEFAULT gen_random_uuid(),
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "updated_at" timestamp with time zone NOT NULL DEFAULT now(),
    "user_id" uuid NOT NULL,
    "title" character varying(255),
    "message" text NOT NULL,
    "type" character varying(50) NOT NULL DEFAULT 'general',
    "read" boolean NOT NULL DEFAULT false,
    "read_at" timestamp with time zone,
    "data" jsonb
);

ALTER TABLE "public"."notification" ENABLE ROW LEVEL SECURITY;

-- Messages table
CREATE TABLE "public"."message" (
    "id" uuid NOT NULL DEFAULT gen_random_uuid(),
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "updated_at" timestamp with time zone NOT NULL DEFAULT now(),
    "sender_id" uuid NOT NULL,
    "receiver_id" uuid NOT NULL,
    "content" text,
    "image_url" character varying(500),
    "read_at" timestamp with time zone
);

ALTER TABLE "public"."message" ENABLE ROW LEVEL SECURITY;

-- Charity table
CREATE TABLE "public"."charity" (
    "id" uuid NOT NULL DEFAULT gen_random_uuid(),
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "updated_at" timestamp with time zone NOT NULL DEFAULT now(),
    "name" character varying(255) NOT NULL,
    "description" text,
    "logo" character varying(500),
    "business_address" text,
    "website" character varying(500),
    "charity_number" character varying(50),
    "is_verified" boolean NOT NULL DEFAULT false
);

ALTER TABLE "public"."charity" ENABLE ROW LEVEL SECURITY;

-- Charity membership table
CREATE TABLE "public"."charity_membership" (
    "id" uuid NOT NULL DEFAULT gen_random_uuid(),
    "created_at" timestamp with time zone NOT NULL DEFAULT now(),
    "user_id" uuid NOT NULL,
    "charity_id" uuid NOT NULL,
    "role" character varying(50) NOT NULL DEFAULT 'member',
    "is_active" boolean NOT NULL DEFAULT true
);

ALTER TABLE "public"."charity_membership" ENABLE ROW LEVEL SECURITY;

-- Create indexes for better performance
CREATE UNIQUE INDEX users_pkey ON public."user" USING btree (id);
CREATE UNIQUE INDEX users_username_key ON public."user" USING btree (username);
CREATE UNIQUE INDEX categories_pkey ON public.category USING btree (id);
CREATE UNIQUE INDEX subcategories_pkey ON public.subcategory USING btree (id);
CREATE UNIQUE INDEX products_pkey ON public.product USING btree (id);
CREATE UNIQUE INDEX tickets_pkey ON public.ticket USING btree (id);
CREATE UNIQUE INDEX payments_pkey ON public.payment USING btree (id);
CREATE UNIQUE INDEX comments_pkey ON public.comment USING btree (id);
CREATE UNIQUE INDEX follows_pkey ON public.follow USING btree (id);
CREATE UNIQUE INDEX notifications_pkey ON public.notification USING btree (id);
CREATE UNIQUE INDEX messages_pkey ON public.message USING btree (id);
CREATE UNIQUE INDEX charities_pkey ON public.charity USING btree (id);
CREATE UNIQUE INDEX charity_memberships_pkey ON public.charity_membership USING btree (id);

-- Create composite indexes for common queries
CREATE INDEX idx_products_user_id ON public.product USING btree (user_id);
CREATE INDEX idx_products_category_id ON public.product USING btree (category_id);
CREATE INDEX idx_products_status ON public.product USING btree (status);
CREATE INDEX idx_tickets_user_id ON public.ticket USING btree (user_id);
CREATE INDEX idx_tickets_product_id ON public.ticket USING btree (product_id);
CREATE INDEX idx_payments_user_id ON public.payment USING btree (user_id);
CREATE INDEX idx_payments_status ON public.payment USING btree (status);
CREATE INDEX idx_notifications_user_id ON public.notification USING btree (user_id);
CREATE INDEX idx_notifications_read ON public.notification USING btree (read);
CREATE INDEX idx_messages_sender_id ON public.message USING btree (sender_id);
CREATE INDEX idx_messages_receiver_id ON public.message USING btree (receiver_id);

-- Unique constraints for business logic
CREATE UNIQUE INDEX unique_follow_relationship ON public.follow USING btree (follower_id, followee_id);
CREATE UNIQUE INDEX unique_charity_membership ON public.charity_membership USING btree (user_id, charity_id);
CREATE UNIQUE INDEX unique_ticket_number_per_product ON public.ticket USING btree (product_id, ticket_number);

-- Add primary key constraints
ALTER TABLE "public"."user" ADD CONSTRAINT "users_pkey" PRIMARY KEY USING INDEX "users_pkey";
ALTER TABLE "public"."category" ADD CONSTRAINT "categories_pkey" PRIMARY KEY USING INDEX "categories_pkey";
ALTER TABLE "public"."subcategory" ADD CONSTRAINT "subcategories_pkey" PRIMARY KEY USING INDEX "subcategories_pkey";
ALTER TABLE "public"."product" ADD CONSTRAINT "products_pkey" PRIMARY KEY USING INDEX "products_pkey";
ALTER TABLE "public"."ticket" ADD CONSTRAINT "tickets_pkey" PRIMARY KEY USING INDEX "tickets_pkey";
ALTER TABLE "public"."payment" ADD CONSTRAINT "payments_pkey" PRIMARY KEY USING INDEX "payments_pkey";
ALTER TABLE "public"."comment" ADD CONSTRAINT "comments_pkey" PRIMARY KEY USING INDEX "comments_pkey";
ALTER TABLE "public"."follow" ADD CONSTRAINT "follows_pkey" PRIMARY KEY USING INDEX "follows_pkey";
ALTER TABLE "public"."notification" ADD CONSTRAINT "notifications_pkey" PRIMARY KEY USING INDEX "notifications_pkey";
ALTER TABLE "public"."message" ADD CONSTRAINT "messages_pkey" PRIMARY KEY USING INDEX "messages_pkey";
ALTER TABLE "public"."charity" ADD CONSTRAINT "charities_pkey" PRIMARY KEY USING INDEX "charities_pkey";
ALTER TABLE "public"."charity_membership" ADD CONSTRAINT "charity_memberships_pkey" PRIMARY KEY USING INDEX "charity_memberships_pkey";

-- Add foreign key constraints
ALTER TABLE "public"."user" ADD CONSTRAINT "users_id_fkey" FOREIGN KEY (id) REFERENCES auth.users(id) ON DELETE CASCADE;

ALTER TABLE "public"."subcategory" ADD CONSTRAINT "subcategories_category_id_fkey" FOREIGN KEY (category_id) REFERENCES category(id) ON DELETE CASCADE;

ALTER TABLE "public"."product" ADD CONSTRAINT "products_user_id_fkey" FOREIGN KEY (user_id) REFERENCES "user"(id) ON DELETE CASCADE;
ALTER TABLE "public"."product" ADD CONSTRAINT "products_category_id_fkey" FOREIGN KEY (category_id) REFERENCES category(id) ON DELETE SET NULL;
ALTER TABLE "public"."product" ADD CONSTRAINT "products_subcategory_id_fkey" FOREIGN KEY (subcategory_id) REFERENCES subcategory(id) ON DELETE SET NULL;
ALTER TABLE "public"."product" ADD CONSTRAINT "products_winner_id_fkey" FOREIGN KEY (winner_id) REFERENCES "user"(id) ON DELETE SET NULL;

ALTER TABLE "public"."ticket" ADD CONSTRAINT "tickets_user_id_fkey" FOREIGN KEY (user_id) REFERENCES "user"(id) ON DELETE CASCADE;
ALTER TABLE "public"."ticket" ADD CONSTRAINT "tickets_product_id_fkey" FOREIGN KEY (product_id) REFERENCES product(id) ON DELETE CASCADE;

ALTER TABLE "public"."payment" ADD CONSTRAINT "payments_user_id_fkey" FOREIGN KEY (user_id) REFERENCES "user"(id) ON DELETE CASCADE;
ALTER TABLE "public"."payment" ADD CONSTRAINT "payments_product_id_fkey" FOREIGN KEY (product_id) REFERENCES product(id) ON DELETE CASCADE;

ALTER TABLE "public"."comment" ADD CONSTRAINT "comments_user_id_fkey" FOREIGN KEY (user_id) REFERENCES "user"(id) ON DELETE CASCADE;
ALTER TABLE "public"."comment" ADD CONSTRAINT "comments_product_id_fkey" FOREIGN KEY (product_id) REFERENCES product(id) ON DELETE CASCADE;

ALTER TABLE "public"."follow" ADD CONSTRAINT "follows_follower_id_fkey" FOREIGN KEY (follower_id) REFERENCES "user"(id) ON DELETE CASCADE;
ALTER TABLE "public"."follow" ADD CONSTRAINT "follows_followee_id_fkey" FOREIGN KEY (followee_id) REFERENCES "user"(id) ON DELETE CASCADE;

ALTER TABLE "public"."notification" ADD CONSTRAINT "notifications_user_id_fkey" FOREIGN KEY (user_id) REFERENCES "user"(id) ON DELETE CASCADE;

ALTER TABLE "public"."message" ADD CONSTRAINT "messages_sender_id_fkey" FOREIGN KEY (sender_id) REFERENCES "user"(id) ON DELETE CASCADE;
ALTER TABLE "public"."message" ADD CONSTRAINT "messages_receiver_id_fkey" FOREIGN KEY (receiver_id) REFERENCES "user"(id) ON DELETE CASCADE;

ALTER TABLE "public"."charity_membership" ADD CONSTRAINT "charity_memberships_user_id_fkey" FOREIGN KEY (user_id) REFERENCES "user"(id) ON DELETE CASCADE;
ALTER TABLE "public"."charity_membership" ADD CONSTRAINT "charity_memberships_charity_id_fkey" FOREIGN KEY (charity_id) REFERENCES charity(id) ON DELETE CASCADE;
