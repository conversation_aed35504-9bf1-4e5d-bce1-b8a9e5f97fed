export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          email: string
          full_name: string | null
          phone: string | null
          address: string | null
          city: string | null
          postal_code: string | null
          country: string | null
          date_of_birth: string | null
          profile_picture_url: string | null
          is_verified: boolean
          verification_token: string | null
          password_reset_token: string | null
          password_reset_expires: string | null
          last_login: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          email: string
          full_name?: string | null
          phone?: string | null
          address?: string | null
          city?: string | null
          postal_code?: string | null
          country?: string | null
          date_of_birth?: string | null
          profile_picture_url?: string | null
          is_verified?: boolean
          verification_token?: string | null
          password_reset_token?: string | null
          password_reset_expires?: string | null
          last_login?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string | null
          phone?: string | null
          address?: string | null
          city?: string | null
          postal_code?: string | null
          country?: string | null
          date_of_birth?: string | null
          profile_picture_url?: string | null
          is_verified?: boolean
          verification_token?: string | null
          password_reset_token?: string | null
          password_reset_expires?: string | null
          last_login?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      categories: {
        Row: {
          id: string
          name: string
          description: string | null
          image_url: string | null
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          image_url?: string | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          image_url?: string | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      products: {
        Row: {
          id: string
          name: string
          description: string
          category_id: string
          image_url: string | null
          retail_price: number
          ticket_price: number
          max_tickets: number
          tickets_sold: number
          status: 'draft' | 'active' | 'sold_out' | 'drawn' | 'completed'
          draw_date: string | null
          winner_id: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          description: string
          category_id: string
          image_url?: string | null
          retail_price: number
          ticket_price: number
          max_tickets: number
          tickets_sold?: number
          status?: 'draft' | 'active' | 'sold_out' | 'drawn' | 'completed'
          draw_date?: string | null
          winner_id?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string
          category_id?: string
          image_url?: string | null
          retail_price?: number
          ticket_price?: number
          max_tickets?: number
          tickets_sold?: number
          status?: 'draft' | 'active' | 'sold_out' | 'drawn' | 'completed'
          draw_date?: string | null
          winner_id?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      tickets: {
        Row: {
          id: string
          user_id: string
          product_id: string
          ticket_number: number
          purchase_date: string
          payment_id: string | null
          is_winner: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          product_id: string
          ticket_number: number
          purchase_date?: string
          payment_id?: string | null
          is_winner?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          product_id?: string
          ticket_number?: number
          purchase_date?: string
          payment_id?: string | null
          is_winner?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      payments: {
        Row: {
          id: string
          user_id: string
          product_id: string
          stripe_payment_intent_id: string
          amount: number
          currency: string
          status: 'pending' | 'succeeded' | 'failed' | 'canceled'
          payment_method: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          product_id: string
          stripe_payment_intent_id: string
          amount: number
          currency: string
          status?: 'pending' | 'succeeded' | 'failed' | 'canceled'
          payment_method?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          product_id?: string
          stripe_payment_intent_id?: string
          amount?: number
          currency?: string
          status?: 'pending' | 'succeeded' | 'failed' | 'canceled'
          payment_method?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      escrow: {
        Row: {
          id: string
          product_id: string
          winner_id: string
          amount: number
          status: 'held' | 'released' | 'refunded'
          stripe_payment_intent_id: string
          delivery_confirmed: boolean
          delivery_confirmed_at: string | null
          released_at: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          product_id: string
          winner_id: string
          amount: number
          status?: 'held' | 'released' | 'refunded'
          stripe_payment_intent_id: string
          delivery_confirmed?: boolean
          delivery_confirmed_at?: string | null
          released_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          product_id?: string
          winner_id?: string
          amount?: number
          status?: 'held' | 'released' | 'refunded'
          stripe_payment_intent_id?: string
          delivery_confirmed?: boolean
          delivery_confirmed_at?: string | null
          released_at?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      notifications: {
        Row: {
          id: string
          user_id: string
          title: string
          message: string
          type: 'info' | 'success' | 'warning' | 'error'
          is_read: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          title: string
          message: string
          type?: 'info' | 'success' | 'warning' | 'error'
          is_read?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          title?: string
          message?: string
          type?: 'info' | 'success' | 'warning' | 'error'
          is_read?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      messages: {
        Row: {
          id: string
          sender_id: string
          recipient_id: string
          product_id: string | null
          subject: string
          content: string
          is_read: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          sender_id: string
          recipient_id: string
          product_id?: string | null
          subject: string
          content: string
          is_read?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          sender_id?: string
          recipient_id?: string
          product_id?: string | null
          subject?: string
          content?: string
          is_read?: boolean
          created_at?: string
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
