# Tombola Raffle App - Revised Project Structure (AWS + Appwrite + Node.js API)

```
tombola-app-v2/
├── aws-backend/                     # Keep existing AWS Lambda structure
│   ├── lambda_functions/            # Your existing Lambda functions
│   │   ├── buy_ticket/
│   │   │   ├── handler.py           # Updated to use Appwrite
│   │   │   └── dockerfile
│   │   ├── draw_raffle/
│   │   ├── confirm_delivery/
│   │   ├── release_escrow/
│   │   ├── notifications/
│   │   └── shared/                  # Updated shared utilities
│   │       ├── appwrite_client.py   # Replace supabase_client.py
│   │       ├── stripe_client.py     # Keep existing
│   │       └── logger.py            # Keep existing
│   ├── template.yaml                # Updated CloudFormation
│   ├── events/                      # Keep existing test events
│   └── tests/                       # Keep existing tests
│
├── api-layer/                       # NEW: Node.js API Gateway
│   ├── src/
│   │   ├── routes/
│   │   │   ├── auth.js
│   │   │   ├── products.js
│   │   │   ├── tickets.js
│   │   │   ├── users.js
│   │   │   └── payments.js
│   │   ├── middleware/
│   │   │   ├── auth.js
│   │   │   ├── validation.js
│   │   │   └── errorHandler.js
│   │   ├── services/
│   │   │   ├── appwrite.js          # Appwrite integration
│   │   │   ├── aws.js               # AWS Lambda invocation
│   │   │   └── stripe.js            # Stripe integration
│   │   ├── utils/
│   │   └── app.js
│   ├── package.json
│   └── Dockerfile
│
├── frontend/                        # React Native (Web-first)
│   ├── src/
│   │   ├── components/
│   │   │   ├── ui/                  # Reusable UI components
│   │   │   ├── forms/               # Form components
│   │   │   └── layout/              # Layout components
│   │   ├── screens/
│   │   │   ├── auth/                # Login, register, etc.
│   │   │   ├── products/            # Product listing, details
│   │   │   ├── tickets/             # Ticket purchasing, history
│   │   │   └── profile/             # User profile, settings
│   │   ├── navigation/
│   │   ├── services/
│   │   │   ├── api.js               # API client (calls Node.js API)
│   │   │   ├── auth.js              # Authentication service
│   │   │   └── storage.js           # Local storage
│   │   ├── hooks/
│   │   ├── utils/
│   │   └── types/
│   ├── package.json
│   ├── metro.config.js
│   ├── app.json
│   └── web/                         # Web-specific configs
│
├── appwrite/                        # Appwrite self-hosted setup
│   ├── docker-compose.yml          # Appwrite deployment
│   ├── collections/                 # Database schema definitions
│   ├── functions/                   # Appwrite cloud functions (if needed)
│   └── appwrite.json               # Appwrite project config
│
└── infrastructure/                  # Deployment & DevOps
    ├── aws/
    │   ├── cloudformation/          # Your existing AWS templates
    │   └── scripts/                 # Deployment scripts
    ├── docker/
    │   └── docker-compose.yml       # Local development
    └── docs/
        ├── api/
        ├── architecture/
        └── deployment/
```

## Revised Architecture Design

### 1. Keep Your AWS Lambda Microservices
- **Buy Ticket Function**: Updated to use Appwrite instead of Supabase
- **Draw Raffle Function**: Keep existing logic, update database calls
- **Confirm Delivery Function**: Keep existing logic
- **Release Escrow Function**: Keep existing Stripe integration
- **Notifications Function**: Keep existing SES integration

### 2. Add Node.js API Layer
- **Purpose**: Bridge between React Native frontend and AWS Lambda backend
- **Responsibilities**:
  - Authentication & authorization
  - Request validation
  - Rate limiting
  - Response formatting
  - Direct Appwrite operations for simple CRUD
  - AWS Lambda invocation for complex business logic

### 3. Database Migration: Supabase → Appwrite
- Self-hosted Appwrite instance
- Same data models, different database
- Better control and scalability
- Built-in authentication and file storage

### 4. React Native Frontend
- Web-first development with React Native Web
- Future mobile app capability
- Single codebase for multiple platforms
- Modern UI with native performance
