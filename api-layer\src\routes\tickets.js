const express = require('express');
const { body, param, query, validationResult } = require('express-validator');
const authMiddleware = require('../middleware/auth');
const appwriteService = require('../services/appwrite');
const awsService = require('../services/aws');
const logger = require('../utils/logger');

const router = express.Router();

/**
 * @route   POST /api/v1/tickets/buy
 * @desc    Purchase a ticket for a raffle
 * @access  Private
 */
router.post('/buy', [
  authMiddleware,
  body('product_id').isUUID().withMessage('Valid product ID is required'),
  body('payment_method_id').notEmpty().withMessage('Payment method ID is required'),
  body('ticket_cost').isFloat({ min: 0.01 }).withMessage('Valid ticket cost is required')
], async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { product_id, payment_method_id, ticket_cost } = req.body;
    const user_id = req.user.id;

    // Verify product exists and is available
    const product = await appwriteService.getProduct(product_id);
    
    if (product.status !== 'active') {
      return res.status(400).json({
        error: 'Product is not available for ticket purchase',
        status: product.status
      });
    }

    // Check if user already has a ticket for this product
    const existingTickets = await appwriteService.getUserTickets(user_id, [
      appwriteService.Query?.equal('product_id', product_id)
    ]);

    if (existingTickets.documents.length > 0) {
      return res.status(400).json({
        error: 'You already have a ticket for this raffle'
      });
    }

    // Prepare data for AWS Lambda function
    const ticketData = {
      product_id,
      user_id,
      payment_method_id,
      ticket_cost: parseFloat(ticket_cost)
    };

    // Call AWS Lambda function to handle the purchase
    const purchaseResult = await awsService.buyTicket(ticketData);

    // Log the successful purchase
    logger.info('Ticket purchased successfully', {
      userId: user_id,
      productId: product_id,
      ticketId: purchaseResult.ticket_id,
      paymentIntentId: purchaseResult.payment_intent_id
    });

    res.status(200).json({
      success: true,
      message: 'Ticket purchased successfully',
      data: {
        ticket_id: purchaseResult.ticket_id,
        ticket_number: purchaseResult.ticket_number,
        payment_intent_id: purchaseResult.payment_intent_id,
        product_name: product.product_name
      }
    });

  } catch (error) {
    logger.error('Error purchasing ticket:', error);
    
    // Handle specific error types
    if (error.message.includes('No tickets available')) {
      return res.status(400).json({
        error: 'No tickets available for this raffle'
      });
    }
    
    if (error.message.includes('Payment failed')) {
      return res.status(400).json({
        error: 'Payment processing failed',
        details: error.message
      });
    }

    res.status(500).json({
      error: 'Failed to purchase ticket',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
});

/**
 * @route   GET /api/v1/tickets/my-tickets
 * @desc    Get current user's tickets
 * @access  Private
 */
router.get('/my-tickets', [
  authMiddleware,
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('offset').optional().isInt({ min: 0 }).withMessage('Offset must be non-negative')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const userId = req.user.id;
    const limit = parseInt(req.query.limit) || 20;
    const offset = parseInt(req.query.offset) || 0;

    // Get user's tickets from Appwrite
    const queries = [
      appwriteService.Query?.limit(limit),
      appwriteService.Query?.offset(offset),
      appwriteService.Query?.orderDesc('purchase_date')
    ].filter(Boolean);

    const tickets = await appwriteService.getUserTickets(userId, queries);

    // Enrich tickets with product information
    const enrichedTickets = await Promise.all(
      tickets.documents.map(async (ticket) => {
        try {
          const product = await appwriteService.getProduct(ticket.product_id);
          return {
            ...ticket,
            product: {
              id: product.$id,
              name: product.product_name,
              description: product.description,
              images: product.images,
              status: product.status,
              raffle_drawn: product.raffle_drawn,
              winner_id: product.winner_id
            }
          };
        } catch (error) {
          logger.warn(`Failed to get product for ticket ${ticket.$id}:`, error);
          return ticket;
        }
      })
    );

    res.status(200).json({
      success: true,
      data: {
        tickets: enrichedTickets,
        total: tickets.total,
        limit,
        offset
      }
    });

  } catch (error) {
    logger.error('Error getting user tickets:', error);
    res.status(500).json({
      error: 'Failed to get tickets',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
});

/**
 * @route   GET /api/v1/tickets/product/:productId
 * @desc    Get tickets for a specific product (for raffle drawing)
 * @access  Private (Admin/Seller only)
 */
router.get('/product/:productId', [
  authMiddleware,
  param('productId').isUUID().withMessage('Valid product ID is required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { productId } = req.params;
    const userId = req.user.id;

    // Get product to verify ownership or admin access
    const product = await appwriteService.getProduct(productId);
    
    // Check if user is the product owner or admin
    if (product.user_id !== userId && req.user.role !== 'admin') {
      return res.status(403).json({
        error: 'Access denied. You can only view tickets for your own products.'
      });
    }

    // Get all tickets for the product
    const tickets = await appwriteService.getProductTickets(productId);

    res.status(200).json({
      success: true,
      data: {
        product_id: productId,
        product_name: product.product_name,
        total_tickets: tickets.total,
        tickets: tickets.documents
      }
    });

  } catch (error) {
    logger.error('Error getting product tickets:', error);
    res.status(500).json({
      error: 'Failed to get product tickets',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
});

/**
 * @route   POST /api/v1/tickets/draw/:productId
 * @desc    Draw a raffle for a product
 * @access  Private (Product owner or admin)
 */
router.post('/draw/:productId', [
  authMiddleware,
  param('productId').isUUID().withMessage('Valid product ID is required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { productId } = req.params;
    const userId = req.user.id;

    // Get product to verify ownership
    const product = await appwriteService.getProduct(productId);
    
    // Check if user is the product owner or admin
    if (product.user_id !== userId && req.user.role !== 'admin') {
      return res.status(403).json({
        error: 'Access denied. You can only draw raffles for your own products.'
      });
    }

    // Call AWS Lambda function to draw the raffle
    const drawResult = await awsService.drawRaffle(productId);

    logger.info('Raffle drawn successfully', {
      productId,
      winner: drawResult.winner,
      drawnBy: userId
    });

    res.status(200).json({
      success: true,
      message: 'Raffle drawn successfully',
      data: {
        product_id: productId,
        winner_id: drawResult.winner,
        drawn_at: new Date().toISOString()
      }
    });

  } catch (error) {
    logger.error('Error drawing raffle:', error);
    
    if (error.message.includes('cannot be drawn yet')) {
      return res.status(400).json({
        error: 'Raffle cannot be drawn yet. Ensure all tickets are sold and raffle has not been drawn already.'
      });
    }

    res.status(500).json({
      error: 'Failed to draw raffle',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
});

module.exports = router;
