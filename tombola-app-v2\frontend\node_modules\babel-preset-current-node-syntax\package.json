{"_from": "babel-preset-current-node-syntax@^1.0.0", "_id": "babel-preset-current-node-syntax@1.1.0", "_inBundle": false, "_integrity": "sha512-ldYss8SbBlWva1bs28q78Ju5Zq1F+8BrqBZZ0VFhLBvhh6lCpC2o3gDJi/5DRLs9FgYZCnmPYIVFU4lRXCkyUw==", "_location": "/babel-preset-current-node-syntax", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "babel-preset-current-node-syntax@^1.0.0", "name": "babel-preset-current-node-syntax", "escapedName": "babel-preset-current-node-syntax", "rawSpec": "^1.0.0", "saveSpec": null, "fetchSpec": "^1.0.0"}, "_requiredBy": ["/babel-preset-jest", "/jest-snapshot"], "_resolved": "https://registry.npmjs.org/babel-preset-current-node-syntax/-/babel-preset-current-node-syntax-1.1.0.tgz", "_shasum": "9a929eafece419612ef4ae4f60b1862ebad8ef30", "_spec": "babel-preset-current-node-syntax@^1.0.0", "_where": "C:\\Users\\<USER>\\OneDrive\\Documents\\Work\\dev\\tombola20\\tombola-app-v2\\frontend\\node_modules\\babel-preset-jest", "author": {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/nicolo-ribaudo"}, "bugs": {"url": "https://github.com/nicolo-ribaudo/babel-preset-current-node-syntax/issues"}, "bundleDependencies": false, "dependencies": {"@babel/plugin-syntax-async-generators": "^7.8.4", "@babel/plugin-syntax-bigint": "^7.8.3", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-class-static-block": "^7.14.5", "@babel/plugin-syntax-import-attributes": "^7.24.7", "@babel/plugin-syntax-import-meta": "^7.10.4", "@babel/plugin-syntax-json-strings": "^7.8.3", "@babel/plugin-syntax-logical-assignment-operators": "^7.10.4", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.8.3", "@babel/plugin-syntax-numeric-separator": "^7.10.4", "@babel/plugin-syntax-object-rest-spread": "^7.8.3", "@babel/plugin-syntax-optional-catch-binding": "^7.8.3", "@babel/plugin-syntax-optional-chaining": "^7.8.3", "@babel/plugin-syntax-private-property-in-object": "^7.14.5", "@babel/plugin-syntax-top-level-await": "^7.14.5"}, "deprecated": false, "description": "A Babel preset that enables parsing of proposals supported by the current Node.js version.", "devDependencies": {"@babel/core": "7.25.2", "@babel/parser-7.0.0": "npm:@babel/parser@7.0.0", "@babel/parser-7.12.0": "npm:@babel/parser@7.12.0", "@babel/parser-7.22.0": "npm:@babel/parser@7.22.0", "@babel/parser-7.9.0": "npm:@babel/parser@7.9.0"}, "homepage": "https://github.com/nicolo-ribaudo/babel-preset-current-node-syntax#readme", "license": "MIT", "main": "src/index.js", "name": "babel-preset-current-node-syntax", "peerDependencies": {"@babel/core": "^7.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/nicolo-ribaudo/babel-preset-current-node-syntax.git"}, "scripts": {"test": "node ./test/index.js"}, "version": "1.1.0"}