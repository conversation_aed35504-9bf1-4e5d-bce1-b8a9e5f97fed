{"_from": "camel-case@^4.1.2", "_id": "camel-case@4.1.2", "_inBundle": false, "_integrity": "sha512-gxGWBrTT1JuMx6R+o5PTXMmUnhnVzLQ9SNutD4YqKtI6ap897t3tKECYla6gCWEkplXnlNybEkZg9GEGxKFCgw==", "_location": "/camel-case", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "camel-case@^4.1.2", "name": "camel-case", "escapedName": "camel-case", "rawSpec": "^4.1.2", "saveSpec": null, "fetchSpec": "^4.1.2"}, "_requiredBy": ["/html-minifier-terser"], "_resolved": "https://registry.npmjs.org/camel-case/-/camel-case-4.1.2.tgz", "_shasum": "9728072a954f805228225a6deea6b38461e1bd5a", "_spec": "camel-case@^4.1.2", "_where": "C:\\Users\\<USER>\\OneDrive\\Documents\\Work\\dev\\tombola20\\tombola-app-v2\\frontend\\node_modules\\html-minifier-terser", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blakeembrey.me"}, "bugs": {"url": "https://github.com/blakeembrey/change-case/issues"}, "bundleDependencies": false, "dependencies": {"pascal-case": "^3.1.2", "tslib": "^2.0.3"}, "deprecated": false, "description": "Transform into a string with the separator denoted by the next word capitalized", "devDependencies": {"@size-limit/preset-small-lib": "^2.2.1", "@types/jest": "^24.0.23", "@types/node": "^12.12.14", "jest": "^24.9.0", "rimraf": "^3.0.0", "ts-jest": "^24.2.0", "tslint": "^5.20.1", "tslint-config-prettier": "^1.18.0", "tslint-config-standard": "^9.0.0", "typescript": "^4.1.2"}, "files": ["dist/", "dist.es2015/", "LICENSE"], "gitHead": "76a21a7f6f2a226521ef6abd345ff309cbd01fb0", "homepage": "https://github.com/blakeembrey/change-case/tree/master/packages/camel-case#readme", "jest": {"roots": ["<rootDir>/src/"], "transform": {"\\.tsx?$": "ts-jest"}, "testRegex": "(/__tests__/.*|\\.(test|spec))\\.(tsx?|jsx?)$", "moduleFileExtensions": ["ts", "tsx", "js", "jsx", "json", "node"]}, "jsnext:main": "dist.es2015/index.js", "keywords": ["camel", "case", "camelcase", "camel-case", "convert", "transform", "identifier"], "license": "MIT", "main": "dist/index.js", "module": "dist.es2015/index.js", "name": "camel-case", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git://github.com/blakeembrey/change-case.git"}, "scripts": {"build": "rimraf dist/ dist.es2015/ && tsc && tsc -P tsconfig.es2015.json", "lint": "tslint \"src/**/*\" --project tsconfig.json", "prepare": "npm run build", "size": "size-limit", "specs": "jest --coverage", "test": "npm run build && npm run lint && npm run specs"}, "sideEffects": false, "size-limit": [{"path": "dist/index.js", "limit": "450 B"}], "typings": "dist/index.d.ts", "version": "4.1.2"}