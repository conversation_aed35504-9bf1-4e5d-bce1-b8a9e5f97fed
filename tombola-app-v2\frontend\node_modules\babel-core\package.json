{"_from": "babel-core@^7.0.0-bridge.0", "_id": "babel-core@7.0.0-bridge.0", "_inBundle": false, "_integrity": "sha512-poPX9mZH/5CSanm50Q+1toVci6pv5KSRv/5TWCwtzQS5XEwn40BcCrgIeMFWP9CKKIniKXNxoIOnOq4VVlGXhg==", "_location": "/babel-core", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "babel-core@^7.0.0-bridge.0", "name": "babel-core", "escapedName": "babel-core", "rawSpec": "^7.0.0-bridge.0", "saveSpec": null, "fetchSpec": "^7.0.0-bridge.0"}, "_requiredBy": ["/jscodeshift"], "_resolved": "https://registry.npmjs.org/babel-core/-/babel-core-7.0.0-bridge.0.tgz", "_shasum": "95a492ddd90f9b4e9a4a1da14eb335b87b634ece", "_spec": "babel-core@^7.0.0-bridge.0", "_where": "C:\\Users\\<USER>\\OneDrive\\Documents\\Work\\dev\\tombola20\\tombola-app-v2\\frontend\\node_modules\\jscodeshift", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bundleDependencies": false, "deprecated": false, "description": "A placeholder package that bridges babel-core to @babel/core.", "devDependencies": {"@babel/core": "^7.0.0-0"}, "files": ["README.md", "index.js"], "license": "MIT", "main": "index.js", "name": "babel-core", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "version": "7.0.0-bridge.0"}