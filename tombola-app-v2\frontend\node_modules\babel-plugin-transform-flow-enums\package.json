{"_from": "babel-plugin-transform-flow-enums@^0.0.2", "_id": "babel-plugin-transform-flow-enums@0.0.2", "_inBundle": false, "_integrity": "sha512-g4aaCrDDOsWjbm0PUUeVnkcVd6AKJsVc/MbnPhEotEpkeJQP6b8nzewohQi7+QS8UyPehOhGWn0nOwjvWpmMvQ==", "_location": "/babel-plugin-transform-flow-enums", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "babel-plugin-transform-flow-enums@^0.0.2", "name": "babel-plugin-transform-flow-enums", "escapedName": "babel-plugin-transform-flow-enums", "rawSpec": "^0.0.2", "saveSpec": null, "fetchSpec": "^0.0.2"}, "_requiredBy": ["/metro-react-native-babel-preset"], "_resolved": "https://registry.npmjs.org/babel-plugin-transform-flow-enums/-/babel-plugin-transform-flow-enums-0.0.2.tgz", "_shasum": "d1d0cc9bdc799c850ca110d0ddc9f21b9ec3ef25", "_spec": "babel-plugin-transform-flow-enums@^0.0.2", "_where": "C:\\Users\\<USER>\\OneDrive\\Documents\\Work\\dev\\tombola20\\tombola-app-v2\\frontend\\node_modules\\metro-react-native-babel-preset", "bugs": {"url": "https://github.com/facebook/flow/issues"}, "bundleDependencies": false, "dependencies": {"@babel/plugin-syntax-flow": "^7.12.1"}, "deprecated": false, "description": "<PERSON>l transform for Flow Enums.", "devDependencies": {"babel-plugin-tester": "^10.0.0", "jest": "^26.6.3"}, "homepage": "https://github.com/facebook/flow#readme", "license": "MIT", "main": "index.js", "name": "babel-plugin-transform-flow-enums", "repository": {"type": "git", "url": "git+https://github.com/facebook/flow.git"}, "scripts": {"test": "jest"}, "version": "0.0.2"}