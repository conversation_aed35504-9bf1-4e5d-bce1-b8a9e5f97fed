# aws-backend/lambda_functions/shared/appwrite_client.py
import os
import json
from appwrite.client import Client
from appwrite.services.databases import Databases
from appwrite.services.storage import Storage
from appwrite.services.account import Account
from appwrite.query import Query
from appwrite.exception import AppwriteException

class AppwriteClient:
    def __init__(self):
        self.client = Client()
        self.client.set_endpoint(os.environ.get('APPWRITE_ENDPOINT', 'https://cloud.appwrite.io/v1'))
        self.client.set_project(os.environ['APPWRITE_PROJECT_ID'])
        self.client.set_key(os.environ['APPWRITE_API_KEY'])
        
        self.databases = Databases(self.client)
        self.storage = Storage(self.client)
        self.account = Account(self.client)
        
        self.database_id = os.environ['APPWRITE_DATABASE_ID']
        
        # Collection IDs
        self.collections = {
            'users': os.environ.get('APPWRITE_COLLECTION_USERS', 'users'),
            'products': os.environ.get('APPWRITE_COLLECTION_PRODUCTS', 'products'),
            'tickets': os.environ.get('APPWRITE_COLLECTION_TICKETS', 'tickets'),
            'categories': os.environ.get('APPWRITE_COLLECTION_CATEGORIES', 'categories'),
            'payments': os.environ.get('APPWRITE_COLLECTION_PAYMENTS', 'payments'),
            'notifications': os.environ.get('APPWRITE_COLLECTION_NOTIFICATIONS', 'notifications'),
            'escrow': os.environ.get('APPWRITE_COLLECTION_ESCROW', 'escrow')
        }

    def create_document(self, collection_name, data, document_id='unique()'):
        """Create a new document in the specified collection"""
        try:
            collection_id = self.collections.get(collection_name)
            if not collection_id:
                raise ValueError(f"Collection '{collection_name}' not found")
            
            return self.databases.create_document(
                database_id=self.database_id,
                collection_id=collection_id,
                document_id=document_id,
                data=data
            )
        except AppwriteException as e:
            raise Exception(f"Failed to create document in {collection_name}: {e.message}")

    def get_document(self, collection_name, document_id):
        """Get a document by ID"""
        try:
            collection_id = self.collections.get(collection_name)
            if not collection_id:
                raise ValueError(f"Collection '{collection_name}' not found")
            
            return self.databases.get_document(
                database_id=self.database_id,
                collection_id=collection_id,
                document_id=document_id
            )
        except AppwriteException as e:
            raise Exception(f"Failed to get document from {collection_name}: {e.message}")

    def update_document(self, collection_name, document_id, data):
        """Update a document"""
        try:
            collection_id = self.collections.get(collection_name)
            if not collection_id:
                raise ValueError(f"Collection '{collection_name}' not found")
            
            return self.databases.update_document(
                database_id=self.database_id,
                collection_id=collection_id,
                document_id=document_id,
                data=data
            )
        except AppwriteException as e:
            raise Exception(f"Failed to update document in {collection_name}: {e.message}")

    def list_documents(self, collection_name, queries=None):
        """List documents with optional queries"""
        try:
            collection_id = self.collections.get(collection_name)
            if not collection_id:
                raise ValueError(f"Collection '{collection_name}' not found")
            
            return self.databases.list_documents(
                database_id=self.database_id,
                collection_id=collection_id,
                queries=queries or []
            )
        except AppwriteException as e:
            raise Exception(f"Failed to list documents from {collection_name}: {e.message}")

    def delete_document(self, collection_name, document_id):
        """Delete a document"""
        try:
            collection_id = self.collections.get(collection_name)
            if not collection_id:
                raise ValueError(f"Collection '{collection_name}' not found")
            
            return self.databases.delete_document(
                database_id=self.database_id,
                collection_id=collection_id,
                document_id=document_id
            )
        except AppwriteException as e:
            raise Exception(f"Failed to delete document from {collection_name}: {e.message}")

    # Convenience methods for specific operations
    def get_product(self, product_id):
        """Get product by ID"""
        return self.get_document('products', product_id)

    def update_product(self, product_id, data):
        """Update product"""
        return self.update_document('products', product_id, data)

    def create_ticket(self, ticket_data):
        """Create a new ticket"""
        return self.create_document('tickets', ticket_data)

    def get_product_tickets(self, product_id):
        """Get all tickets for a product"""
        queries = [Query.equal('product_id', product_id)]
        return self.list_documents('tickets', queries)

    def get_user_tickets(self, user_id):
        """Get all tickets for a user"""
        queries = [Query.equal('user_id', user_id)]
        return self.list_documents('tickets', queries)

    def create_payment(self, payment_data):
        """Create a payment record"""
        return self.create_document('payments', payment_data)

    def update_payment(self, payment_id, data):
        """Update payment status"""
        return self.update_document('payments', payment_id, data)

    def create_notification(self, notification_data):
        """Create a notification"""
        return self.create_document('notifications', notification_data)

    def get_user_notifications(self, user_id, unread_only=False):
        """Get notifications for a user"""
        queries = [Query.equal('user_id', user_id)]
        if unread_only:
            queries.append(Query.equal('read', False))
        return self.list_documents('notifications', queries)

    def create_escrow(self, escrow_data):
        """Create an escrow record"""
        return self.create_document('escrow', escrow_data)

    def update_escrow(self, escrow_id, data):
        """Update escrow status"""
        return self.update_document('escrow', escrow_id, data)

    def get_escrow_by_product(self, product_id):
        """Get escrow record by product ID"""
        queries = [Query.equal('product_id', product_id)]
        result = self.list_documents('escrow', queries)
        return result['documents'][0] if result['documents'] else None

# Create a singleton instance
appwrite = AppwriteClient()
