version: '3.8'

services:
  # Supabase Database (PostgreSQL)
  supabase-db:
    image: supabase/postgres:**********
    container_name: tombola-supabase-db
    restart: unless-stopped
    environment:
      POSTGRES_DB: postgres
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_HOST_AUTH_METHOD: trust
      POSTGRES_PORT: 5432
    ports:
      - "5432:5432"
    volumes:
      - supabase_db_data:/var/lib/postgresql/data
      - ./supabase/migrations:/docker-entrypoint-initdb.d
    networks:
      - tombola-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
    command: >
      postgres
      -c wal_level=logical
      -c max_replication_slots=5
      -c max_wal_senders=10

  # Supabase Studio (GUI)
  supabase-studio:
    image: supabase/studio:20240101-ce42139
    container_name: tombola-supabase-studio
    restart: unless-stopped
    environment:
      SUPABASE_URL: http://supabase-kong:8000
      SUPABASE_REST_URL: http://localhost:8000/rest/v1/
      SUPABASE_ANON_KEY: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0
      SUPABASE_SERVICE_KEY: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU
      STUDIO_PG_META_URL: http://supabase-meta:8080
      POSTGRES_PASSWORD: postgres
    ports:
      - "3010:3000"
    depends_on:
      supabase-db:
        condition: service_healthy
    networks:
      - tombola-network

  # Supabase Kong (API Gateway)
  supabase-kong:
    image: kong:2.8.1
    container_name: tombola-supabase-kong
    restart: unless-stopped
    environment:
      KONG_DATABASE: "off"
      KONG_DECLARATIVE_CONFIG: /var/lib/kong/kong.yml
      KONG_DNS_ORDER: LAST,A,CNAME
      KONG_PLUGINS: request-transformer,cors,key-auth,acl,basic-auth
      KONG_NGINX_PROXY_PROXY_BUFFER_SIZE: 160k
      KONG_NGINX_PROXY_PROXY_BUFFERS: 64 160k
    ports:
      - "8000:8000"
      - "8443:8443"
    volumes:
      - ./supabase/config/kong.yml:/var/lib/kong/kong.yml:ro
    depends_on:
      - supabase-db
    networks:
      - tombola-network

  # Supabase GoTrue (Auth)
  supabase-auth:
    image: supabase/gotrue:v2.132.3
    container_name: tombola-supabase-auth
    restart: unless-stopped
    environment:
      GOTRUE_API_HOST: 0.0.0.0
      GOTRUE_API_PORT: 9999
      API_EXTERNAL_URL: http://localhost:8000
      GOTRUE_DB_DRIVER: postgres
      GOTRUE_DB_DATABASE_URL: ********************************************************/postgres
      GOTRUE_SITE_URL: http://localhost:3000
      GOTRUE_URI_ALLOW_LIST: "*"
      GOTRUE_DISABLE_SIGNUP: "false"
      GOTRUE_JWT_ADMIN_ROLES: service_role
      GOTRUE_JWT_AUD: authenticated
      GOTRUE_JWT_DEFAULT_GROUP_NAME: authenticated
      GOTRUE_JWT_EXP: 3600
      GOTRUE_JWT_SECRET: super-secret-jwt-token-with-at-least-32-characters-long
      GOTRUE_EXTERNAL_EMAIL_ENABLED: "true"
      GOTRUE_MAILER_AUTOCONFIRM: "true"
      GOTRUE_SMTP_ADMIN_EMAIL: <EMAIL>
      GOTRUE_SMTP_HOST: supabase-inbucket
      GOTRUE_SMTP_PORT: 2500
      GOTRUE_SMTP_USER: fake_mail_user
      GOTRUE_SMTP_PASS: fake_mail_password
      GOTRUE_SMTP_SENDER_NAME: fake_sender
    depends_on:
      supabase-db:
        condition: service_healthy
    networks:
      - tombola-network

  # Supabase PostgREST (API)
  supabase-rest:
    image: postgrest/postgrest:v11.2.2
    container_name: tombola-supabase-rest
    restart: unless-stopped
    environment:
      PGRST_DB_URI: **************************************************/postgres
      PGRST_DB_SCHEMAS: public,storage,graphql_public
      PGRST_DB_ANON_ROLE: anon
      PGRST_JWT_SECRET: super-secret-jwt-token-with-at-least-32-characters-long
      PGRST_DB_USE_LEGACY_GUCS: "false"
      PGRST_APP_SETTINGS_JWT_SECRET: super-secret-jwt-token-with-at-least-32-characters-long
      PGRST_APP_SETTINGS_JWT_EXP: 3600
    depends_on:
      supabase-db:
        condition: service_healthy
    networks:
      - tombola-network

  # Supabase Realtime
  supabase-realtime:
    image: supabase/realtime:v2.25.50
    container_name: tombola-supabase-realtime
    restart: unless-stopped
    environment:
      PORT: 4000
      DB_HOST: supabase-db
      DB_PORT: 5432
      DB_USER: supabase_admin
      DB_PASSWORD: postgres
      DB_NAME: postgres
      DB_AFTER_CONNECT_QUERY: 'SET search_path TO _realtime'
      DB_ENC_KEY: supabaserealtime
      API_JWT_SECRET: super-secret-jwt-token-with-at-least-32-characters-long
      FLY_ALLOC_ID: fly123
      FLY_APP_NAME: realtime
      SECRET_KEY_BASE: UpNVntn3cDxHJpq99YMc1T1AQgQpc8kfYTuRgBiYa15BLrx8etQoXz3gZv1/u2oq
      ERL_AFLAGS: -proto_dist inet_tcp
      ENABLE_TAILSCALE: "false"
      DNS_NODES: "''"
    depends_on:
      supabase-db:
        condition: service_healthy
    networks:
      - tombola-network

  # Supabase Meta (Database management)
  supabase-meta:
    image: supabase/postgres-meta:v0.68.0
    container_name: tombola-supabase-meta
    restart: unless-stopped
    environment:
      PG_META_PORT: 8080
      PG_META_DB_HOST: supabase-db
      PG_META_DB_PORT: 5432
      PG_META_DB_NAME: postgres
      PG_META_DB_USER: supabase_admin
      PG_META_DB_PASSWORD: postgres
    depends_on:
      supabase-db:
        condition: service_healthy
    networks:
      - tombola-network

  # Supabase Storage
  supabase-storage:
    image: supabase/storage-api:v0.40.4
    container_name: tombola-supabase-storage
    restart: unless-stopped
    environment:
      ANON_KEY: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0
      SERVICE_KEY: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU
      POSTGREST_URL: http://supabase-rest:3000
      PGRST_JWT_SECRET: super-secret-jwt-token-with-at-least-32-characters-long
      DATABASE_URL: ***********************************************************/postgres
      FILE_SIZE_LIMIT: 52428800
      STORAGE_BACKEND: file
      FILE_STORAGE_BACKEND_PATH: /var/lib/storage
      TENANT_ID: stub
      REGION: stub
      GLOBAL_S3_BUCKET: stub
      ENABLE_IMAGE_TRANSFORMATION: "true"
      IMGPROXY_URL: http://supabase-imgproxy:5001
    volumes:
      - supabase_storage_data:/var/lib/storage
    depends_on:
      supabase-db:
        condition: service_healthy
    networks:
      - tombola-network

  # Supabase Image Proxy
  supabase-imgproxy:
    image: darthsim/imgproxy:v3.8.0
    container_name: tombola-supabase-imgproxy
    restart: unless-stopped
    environment:
      IMGPROXY_BIND: ":5001"
      IMGPROXY_LOCAL_FILESYSTEM_ROOT: /
      IMGPROXY_USE_ETAG: "true"
      IMGPROXY_ENABLE_WEBP_DETECTION: "true"
    volumes:
      - supabase_storage_data:/var/lib/storage:ro
    networks:
      - tombola-network

  # Supabase Inbucket (Email testing)
  supabase-inbucket:
    image: inbucket/inbucket:3.0.3
    container_name: tombola-supabase-inbucket
    restart: unless-stopped
    environment:
      INBUCKET_WEB_ADDR: 0.0.0.0:9000
      INBUCKET_POP3_ADDR: 0.0.0.0:1100
      INBUCKET_SMTP_ADDR: 0.0.0.0:2500
    ports:
      - "9000:9000"
    networks:
      - tombola-network

  # Fastify API Gateway
  api:
    build:
      context: ./api-layer
      dockerfile: Dockerfile
    container_name: tombola-api
    restart: unless-stopped
    environment:
      NODE_ENV: development
      PORT: 3001
      DATABASE_URL: ***********************************************/postgres
      SUPABASE_URL: http://supabase-kong:8000
      SUPABASE_ANON_KEY: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0
      SUPABASE_SERVICE_KEY: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU
      JWT_SECRET: super-secret-jwt-token-with-at-least-32-characters-long
      CORS_ORIGIN: http://localhost:3000
    ports:
      - "3001:3001"
    depends_on:
      supabase-db:
        condition: service_healthy
    volumes:
      - ./api-layer:/app
      - /app/node_modules
    networks:
      - tombola-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # React Native Frontend (Web)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: tombola-frontend
    restart: unless-stopped
    environment:
      NODE_ENV: development
      EXPO_PUBLIC_API_URL: http://localhost:3001
      EXPO_PUBLIC_WEB_PORT: 3000
    ports:
      - "3000:3000"
      - "8081:8081"  # Metro bundler
    depends_on:
      - api
    volumes:
      - ./frontend:/app
      - /app/node_modules
    networks:
      - tombola-network
    stdin_open: true
    tty: true

  # Redis for caching (optional but recommended)
  redis:
    image: redis:7-alpine
    container_name: tombola-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - tombola-network
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

  # Nginx reverse proxy (optional for production-like setup)
  nginx:
    image: nginx:alpine
    container_name: tombola-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - api
      - frontend
    networks:
      - tombola-network

volumes:
  supabase_db_data:
    driver: local
  supabase_storage_data:
    driver: local
  redis_data:
    driver: local

networks:
  tombola-network:
    driver: bridge
