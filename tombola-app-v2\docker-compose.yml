version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: tombola-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: tombola_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    networks:
      - tombola-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d tombola_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Fastify API Gateway
  api:
    build:
      context: ./api-layer
      dockerfile: Dockerfile
    container_name: tombola-api
    restart: unless-stopped
    environment:
      NODE_ENV: development
      PORT: 3001
      DATABASE_URL: ********************************************/tombola_db
      SUPABASE_URL: http://postgres:5432
      SUPABASE_ANON_KEY: your-anon-key-here
      SUPABASE_SERVICE_KEY: your-service-key-here
      JWT_SECRET: your-jwt-secret-here
      CORS_ORIGIN: http://localhost:3000
    ports:
      - "3001:3001"
    depends_on:
      postgres:
        condition: service_healthy
    volumes:
      - ./api-layer:/app
      - /app/node_modules
    networks:
      - tombola-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # React Native Frontend (Web)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: tombola-frontend
    restart: unless-stopped
    environment:
      NODE_ENV: development
      EXPO_PUBLIC_API_URL: http://localhost:3001
      EXPO_PUBLIC_WEB_PORT: 3000
    ports:
      - "3000:3000"
      - "8081:8081"  # Metro bundler
    depends_on:
      - api
    volumes:
      - ./frontend:/app
      - /app/node_modules
    networks:
      - tombola-network
    stdin_open: true
    tty: true

  # Redis for caching (optional but recommended)
  redis:
    image: redis:7-alpine
    container_name: tombola-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - tombola-network
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

  # Nginx reverse proxy (optional for production-like setup)
  nginx:
    image: nginx:alpine
    container_name: tombola-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - api
      - frontend
    networks:
      - tombola-network

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  tombola-network:
    driver: bridge
