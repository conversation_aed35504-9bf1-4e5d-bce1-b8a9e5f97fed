{"name": "tombola-api-layer", "version": "1.0.0", "description": "Fastify TypeScript API Gateway for Tombola Raffle App - bridges React Native frontend with AWS Lambda backend", "main": "dist/app.js", "scripts": {"build": "tsc", "start": "node dist/server.js", "dev": "tsx watch src/server.ts", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/ --ext .ts", "lint:fix": "eslint src/ --ext .ts --fix", "type-check": "tsc --noEmit", "docker:build": "docker build -t tombola-api .", "docker:run": "docker run -p 3000:3000 tombola-api"}, "dependencies": {"fastify": "^4.24.3", "@fastify/cors": "^8.4.0", "@fastify/helmet": "^11.1.1", "@fastify/rate-limit": "^8.0.3", "@fastify/jwt": "^7.2.4", "@fastify/multipart": "^8.0.0", "@fastify/swagger": "^8.12.0", "@fastify/swagger-ui": "^2.1.0", "fastify-type-provider-zod": "^1.1.9", "zod": "^3.22.4", "@supabase/supabase-js": "^2.38.4", "aws-sdk": "^2.1467.0", "stripe": "^13.6.0", "dotenv": "^16.3.1", "pino": "^8.16.1", "pino-pretty": "^10.2.3", "uuid": "^9.0.0"}, "devDependencies": {"@types/node": "^20.8.7", "typescript": "^5.2.2", "tsx": "^3.14.0", "jest": "^29.7.0", "@types/jest": "^29.5.6", "ts-jest": "^29.1.1", "@typescript-eslint/eslint-plugin": "^6.9.0", "@typescript-eslint/parser": "^6.9.0", "eslint": "^8.52.0", "eslint-config-prettier": "^9.0.0", "prettier": "^3.0.3"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "roots": ["<rootDir>/src", "<rootDir>/tests"], "testMatch": ["**/__tests__/**/*.ts", "**/?(*.)+(spec|test).ts"], "collectCoverageFrom": ["src/**/*.ts", "!src/**/*.d.ts"]}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": ["api", "fastify", "typescript", "tombola", "raffle", "supabase", "aws-lambda", "react-native"], "author": "Tombola Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-org/tombola-app-v2.git"}}