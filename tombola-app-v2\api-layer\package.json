{"name": "tombola-api-layer", "version": "1.0.0", "description": "Node.js API layer for Tombola Raffle App - bridges React Native frontend with AWS Lambda backend", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "build": "echo 'No build step required'", "docker:build": "docker build -t tombola-api .", "docker:run": "docker run -p 3000:3000 tombola-api"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.0.0", "express-rate-limit": "^6.8.1", "express-validator": "^7.0.1", "jsonwebtoken": "^9.0.1", "bcryptjs": "^2.4.3", "node-appwrite": "^11.0.0", "aws-sdk": "^2.1467.0", "stripe": "^13.6.0", "dotenv": "^16.3.1", "winston": "^3.10.0", "compression": "^1.7.4", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "redis": "^4.6.7", "bull": "^4.11.3", "nodemailer": "^6.9.4", "uuid": "^9.0.0"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.1", "supertest": "^6.3.3", "eslint": "^8.45.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.27.5", "@types/jest": "^29.5.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": ["api", "nodejs", "express", "tombola", "raffle", "appwrite", "aws-lambda", "react-native"], "author": "Tombola Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-org/tombola-app-v2.git"}}