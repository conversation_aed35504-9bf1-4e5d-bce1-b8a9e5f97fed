{"_from": "babel-plugin-polyfill-corejs3@^0.11.0", "_id": "babel-plugin-polyfill-corejs3@0.11.1", "_inBundle": false, "_integrity": "sha512-yGCqvBT4rwMczo28xkH/noxJ6MZ4nJfkVYdoDaC/utLtWrXxv27HVrzAeSbqR8SxDsp46n0YF47EbHoixy6rXQ==", "_location": "/babel-plugin-polyfill-corejs3", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "babel-plugin-polyfill-corejs3@^0.11.0", "name": "babel-plugin-polyfill-corejs3", "escapedName": "babel-plugin-polyfill-corejs3", "rawSpec": "^0.11.0", "saveSpec": null, "fetchSpec": "^0.11.0"}, "_requiredBy": ["/@babel/plugin-transform-runtime", "/@babel/preset-env"], "_resolved": "https://registry.npmjs.org/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.11.1.tgz", "_shasum": "4e4e182f1bb37c7ba62e2af81d8dd09df31344f6", "_spec": "babel-plugin-polyfill-corejs3@^0.11.0", "_where": "C:\\Users\\<USER>\\OneDrive\\Documents\\Work\\dev\\tombola20\\tombola-app-v2\\frontend\\node_modules\\@babel\\preset-env", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-define-polyfill-provider": "^0.6.3", "core-js-compat": "^3.40.0"}, "deprecated": false, "description": "A Babel plugin to inject imports to core-js@3 polyfills", "devDependencies": {"@babel/core": "^7.22.6", "@babel/helper-plugin-test-runner": "^7.22.5", "@babel/plugin-proposal-decorators": "^7.22.15", "@babel/plugin-transform-class-properties": "^7.22.5", "@babel/plugin-transform-classes": "^7.22.6", "@babel/plugin-transform-for-of": "^7.22.5", "@babel/plugin-transform-modules-commonjs": "^7.22.5", "@babel/plugin-transform-runtime": "^7.22.15", "@babel/plugin-transform-spread": "^7.22.5", "core-js": "^3.40.0", "core-js-pure": "^3.40.0"}, "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "786a49e4fc05408168aefd9df018a56dcf97c450", "homepage": "https://github.com/babel/babel-polyfills#readme", "keywords": ["babel-plugin"], "license": "MIT", "main": "lib/index.js", "name": "babel-plugin-polyfill-corejs3", "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel-polyfills.git", "directory": "packages/babel-plugin-polyfill-corejs3"}, "version": "0.11.1"}