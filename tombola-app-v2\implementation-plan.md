# Tombola Raffle App - Revised Implementation Plan
## (Keeping AWS Lambda + Adding Appwrite + Node.js API + React Native)

## Phase 1: Appwrite Setup & Database Migration (Week 1-2)

### 1.1 Self-Hosted Appwrite Setup
```bash
# Clone Appwrite
git clone https://github.com/appwrite/appwrite.git
cd appwrite

# Start Appwrite with <PERSON><PERSON>
docker-compose up -d

# Install Appwrite CLI
npm install -g appwrite-cli

# Initialize project
appwrite init project
```

### 1.2 Database Collections Design
```javascript
// Collections to create in Appwrite
const collections = {
  users: {
    attributes: [
      { key: 'username', type: 'string', size: 255, required: true },
      { key: 'email', type: 'string', size: 255, required: true },
      { key: 'profile_picture', type: 'string', size: 500 },
      { key: 'bio', type: 'string', size: 1000 },
      { key: 'role', type: 'enum', elements: ['user', 'charity', 'admin'] },
      { key: 'stripe_account_id', type: 'string', size: 255 },
      { key: 'created_at', type: 'datetime', required: true },
      { key: 'updated_at', type: 'datetime', required: true }
    ],
    indexes: [
      { key: 'email_index', type: 'unique', attributes: ['email'] },
      { key: 'username_index', type: 'unique', attributes: ['username'] }
    ]
  },
  
  products: {
    attributes: [
      { key: 'user_id', type: 'string', size: 36, required: true },
      { key: 'product_name', type: 'string', size: 255, required: true },
      { key: 'description', type: 'string', size: 2000 },
      { key: 'price', type: 'double', required: true },
      { key: 'ticket_cost', type: 'double', required: true },
      { key: 'ticket_count', type: 'integer', required: true },
      { key: 'tickets_sold', type: 'integer', default: 0 },
      { key: 'category_id', type: 'string', size: 36 },
      { key: 'images', type: 'string', array: true },
      { key: 'status', type: 'enum', elements: ['active', 'sold_out', 'drawn', 'completed'] },
      { key: 'raffle_drawn', type: 'boolean', default: false },
      { key: 'winner_id', type: 'string', size: 36 },
      { key: 'product_sent', type: 'boolean', default: false },
      { key: 'product_delivered', type: 'boolean', default: false },
      { key: 'created_at', type: 'datetime', required: true },
      { key: 'draw_date', type: 'datetime' }
    ],
    indexes: [
      { key: 'user_products', type: 'key', attributes: ['user_id'] },
      { key: 'category_products', type: 'key', attributes: ['category_id'] },
      { key: 'status_index', type: 'key', attributes: ['status'] }
    ]
  },
  
  tickets: {
    attributes: [
      { key: 'user_id', type: 'string', size: 36, required: true },
      { key: 'product_id', type: 'string', size: 36, required: true },
      { key: 'payment_intent_id', type: 'string', size: 255 },
      { key: 'ticket_number', type: 'integer', required: true },
      { key: 'purchase_date', type: 'datetime', required: true },
      { key: 'status', type: 'enum', elements: ['pending', 'confirmed', 'refunded'] }
    ],
    indexes: [
      { key: 'user_tickets', type: 'key', attributes: ['user_id'] },
      { key: 'product_tickets', type: 'key', attributes: ['product_id'] },
      { key: 'unique_ticket', type: 'unique', attributes: ['product_id', 'ticket_number'] }
    ]
  },
  
  categories: {
    attributes: [
      { key: 'name', type: 'string', size: 255, required: true },
      { key: 'description', type: 'string', size: 1000 },
      { key: 'image', type: 'string', size: 500 },
      { key: 'active', type: 'boolean', default: true }
    ]
  },
  
  payments: {
    attributes: [
      { key: 'user_id', type: 'string', size: 36, required: true },
      { key: 'product_id', type: 'string', size: 36, required: true },
      { key: 'ticket_id', type: 'string', size: 36, required: true },
      { key: 'stripe_payment_intent_id', type: 'string', size: 255, required: true },
      { key: 'amount', type: 'double', required: true },
      { key: 'currency', type: 'string', size: 3, default: 'AUD' },
      { key: 'status', type: 'enum', elements: ['pending', 'confirmed', 'captured', 'refunded'] },
      { key: 'created_at', type: 'datetime', required: true }
    ]
  },
  
  notifications: {
    attributes: [
      { key: 'user_id', type: 'string', size: 36, required: true },
      { key: 'title', type: 'string', size: 255, required: true },
      { key: 'message', type: 'string', size: 1000, required: true },
      { key: 'type', type: 'enum', elements: ['info', 'success', 'warning', 'error'] },
      { key: 'read', type: 'boolean', default: false },
      { key: 'created_at', type: 'datetime', required: true }
    ]
  }
};
```

### 1.3 API Gateway Setup
```javascript
// package.json for API Gateway
{
  "name": "tombola-api-gateway",
  "version": "1.0.0",
  "dependencies": {
    "express": "^4.18.2",
    "cors": "^2.8.5",
    "helmet": "^7.0.0",
    "express-rate-limit": "^6.8.1",
    "http-proxy-middleware": "^2.0.6",
    "jsonwebtoken": "^9.0.1",
    "node-appwrite": "^11.0.0",
    "dotenv": "^16.3.1",
    "winston": "^3.10.0"
  }
}
```

## Phase 2: Update AWS Lambda Functions (Week 3-4)

### 2.1 Replace Supabase with Appwrite in Lambda Functions
```python
# aws-backend/lambda_functions/shared/appwrite_client.py
from appwrite.client import Client
from appwrite.services.databases import Databases
from appwrite.services.storage import Storage
import os

class AppwriteClient:
    def __init__(self):
        self.client = Client()
        self.client.set_endpoint(os.environ['APPWRITE_ENDPOINT'])
        self.client.set_project(os.environ['APPWRITE_PROJECT_ID'])
        self.client.set_key(os.environ['APPWRITE_API_KEY'])

        self.databases = Databases(self.client)
        self.storage = Storage(self.client)
        self.database_id = os.environ['APPWRITE_DATABASE_ID']
```

### 2.2 Update Lambda Function Handlers
- Update `buy_ticket/handler.py` to use Appwrite
- Update `draw_raffle/handler.py` to use Appwrite
- Update `confirm_delivery/handler.py` to use Appwrite
- Update `release_escrow/handler.py` to use Appwrite
- Keep existing Stripe and SNS integrations

### 2.3 Update CloudFormation Template
- Add Appwrite environment variables to Lambda functions
- Keep existing SNS topics and API Gateway setup
- Update IAM permissions if needed

## Phase 3: Node.js API Layer Development (Week 5-7)

### 3.1 API Gateway Setup
```javascript
// api-layer/src/app.js - Express server
// Routes that call AWS Lambda functions for complex operations
// Direct Appwrite calls for simple CRUD operations
```

### 3.2 Key API Endpoints
- `POST /api/auth/login` → Direct Appwrite auth
- `POST /api/auth/register` → Direct Appwrite auth
- `GET /api/products` → Direct Appwrite query
- `POST /api/tickets/buy` → Call AWS Lambda buy_ticket function
- `POST /api/raffle/draw` → Call AWS Lambda draw_raffle function
- `GET /api/users/profile` → Direct Appwrite query

### 3.3 AWS Lambda Integration
```javascript
// api-layer/src/services/aws.js
const AWS = require('aws-sdk');
const lambda = new AWS.Lambda();

const invokeLambda = async (functionName, payload) => {
  const params = {
    FunctionName: functionName,
    Payload: JSON.stringify(payload)
  };
  return await lambda.invoke(params).promise();
};
```

## Phase 4: React Native Frontend (Week 8-11)

### 4.1 Setup React Native with Web Support
```bash
npx create-expo-app tombola-frontend --template
cd tombola-frontend
npx expo install react-native-web
```

### 4.2 Core Features Implementation
- Authentication screens (login/register)
- Product browsing and search
- Ticket purchasing flow
- User dashboard and ticket history
- Raffle results and notifications

### 4.3 API Integration
```javascript
// frontend/src/services/api.js
const API_BASE = 'http://localhost:3000/api';

export const buyTicket = async (productId, paymentMethodId) => {
  const response = await fetch(`${API_BASE}/tickets/buy`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ productId, paymentMethodId })
  });
  return response.json();
};
```

## Phase 5: Testing & Deployment (Week 12-13)

### 5.1 Testing Strategy
- Unit tests for Lambda functions (keep existing)
- Integration tests for API layer
- E2E tests for React Native app
- Load testing for the complete system

### 5.2 Deployment
- Deploy Appwrite to production server
- Update AWS Lambda functions with new Appwrite config
- Deploy Node.js API layer (Docker + ECS/EC2)
- Deploy React Native web app (Vercel/Netlify)
- Set up monitoring and logging
