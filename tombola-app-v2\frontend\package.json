{"name": "tombola-frontend", "version": "1.0.0", "description": "React Native frontend for Tombola Raffle App (Web-first, mobile-ready)", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "web": "expo start --web", "android": "expo start --android", "ios": "expo start --ios", "build:web": "expo build:web", "build:android": "expo build:android", "build:ios": "expo build:ios", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "type-check": "tsc --noEmit"}, "dependencies": {"expo": "~49.0.0", "expo-status-bar": "~1.6.0", "react": "18.2.0", "react-native": "0.72.6", "react-native-web": "~0.19.6", "react-dom": "18.2.0", "@expo/webpack-config": "^19.0.0", "@react-navigation/native": "^6.1.7", "@react-navigation/stack": "^6.3.17", "@react-navigation/bottom-tabs": "^6.5.8", "@react-navigation/drawer": "^6.6.3", "react-native-screens": "~3.22.0", "react-native-safe-area-context": "4.6.3", "react-native-gesture-handler": "~2.12.0", "react-native-reanimated": "~3.3.0", "@tanstack/react-query": "^4.32.6", "react-hook-form": "^7.45.4", "@hookform/resolvers": "^3.3.1", "zod": "^3.22.2", "axios": "^1.5.0", "@react-native-async-storage/async-storage": "1.18.2", "react-native-toast-message": "^2.1.6", "react-native-modal": "^13.0.1", "react-native-vector-icons": "^10.0.0", "@expo/vector-icons": "^13.0.0", "react-native-image-picker": "^5.6.0", "react-native-paper": "^5.10.4", "react-native-elements": "^3.4.3", "styled-components": "^6.0.7", "date-fns": "^2.30.0", "react-native-uuid": "^2.0.1", "@stripe/stripe-react-native": "^0.35.0", "expo-camera": "~13.4.2", "expo-image-picker": "~14.3.2", "expo-notifications": "~0.20.1", "expo-secure-store": "~12.3.1", "expo-linking": "~5.0.2", "expo-constants": "~14.4.2"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.2.14", "@types/react-native": "~0.72.2", "@typescript-eslint/eslint-plugin": "^6.2.1", "@typescript-eslint/parser": "^6.2.1", "eslint": "^8.45.0", "eslint-config-expo": "^7.0.0", "eslint-plugin-react": "^7.33.1", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-native": "^4.0.0", "jest": "^29.6.1", "jest-expo": "~49.0.0", "@testing-library/react-native": "^12.2.2", "@testing-library/jest-native": "^5.4.2", "typescript": "^5.1.3", "metro-config": "^0.76.0"}, "jest": {"preset": "jest-expo", "setupFilesAfterEnv": ["@testing-library/jest-native/extend-expect"], "transformIgnorePatterns": ["node_modules/(?!((jest-)?react-native|@react-native(-community)?)|expo(nent)?|@expo(nent)?/.*|@expo-google-fonts/.*|react-navigation|@react-navigation/.*|@unimodules/.*|unimodules|sentry-expo|native-base|react-native-svg)"]}, "private": true, "expo": {"name": "Tombola Raffle", "slug": "tombola-raffle", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.tombola.raffle"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#FFFFFF"}, "package": "com.tombola.raffle"}, "web": {"favicon": "./assets/favicon.png", "bundler": "webpack"}, "plugins": ["expo-camera", "expo-image-picker", ["expo-notifications", {"icon": "./assets/notification-icon.png", "color": "#ffffff"}]]}, "keywords": ["react-native", "expo", "tombola", "raffle", "web", "mobile", "typescript"], "author": "Tombola Team", "license": "MIT"}