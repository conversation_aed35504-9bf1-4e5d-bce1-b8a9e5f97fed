{"_from": "babel-preset-jest@^29.6.3", "_id": "babel-preset-jest@29.6.3", "_inBundle": false, "_integrity": "sha512-0B3bhxR6snWXJZtR/RliHTDPRgn1sNHOR0yVtq/IiQFyuOVjFS+wuio/R4gSNkyYmKmJB4wGZv2NZanmKmTnNA==", "_location": "/babel-preset-jest", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "babel-preset-jest@^29.6.3", "name": "babel-preset-jest", "escapedName": "babel-preset-jest", "rawSpec": "^29.6.3", "saveSpec": null, "fetchSpec": "^29.6.3"}, "_requiredBy": ["/babel-jest"], "_resolved": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-29.6.3.tgz", "_shasum": "fa05fa510e7d493896d7b0dd2033601c840f171c", "_spec": "babel-preset-jest@^29.6.3", "_where": "C:\\Users\\<USER>\\OneDrive\\Documents\\Work\\dev\\tombola20\\tombola-app-v2\\frontend\\node_modules\\babel-jest", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "bundleDependencies": false, "dependencies": {"babel-plugin-jest-hoist": "^29.6.3", "babel-preset-current-node-syntax": "^1.0.0"}, "deprecated": false, "description": "> Babel preset for all Jest plugins. This preset is automatically included when using [babel-jest](https://github.com/jestjs/jest/tree/main/packages/babel-jest).", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "gitHead": "fb7d95c8af6e0d65a8b65348433d8a0ea0725b5b", "homepage": "https://github.com/jestjs/jest#readme", "license": "MIT", "main": "./index.js", "name": "babel-preset-jest", "peerDependencies": {"@babel/core": "^7.0.0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/jestjs/jest.git", "directory": "packages/babel-preset-jest"}, "version": "29.6.3"}