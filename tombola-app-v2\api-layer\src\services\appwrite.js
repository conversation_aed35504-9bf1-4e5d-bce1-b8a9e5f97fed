const { Client, Account, Databases, Storage, Query } = require('node-appwrite');
const logger = require('../utils/logger');

class AppwriteService {
  constructor() {
    this.client = new Client();
    this.client
      .setEndpoint(process.env.APPWRITE_ENDPOINT || 'https://cloud.appwrite.io/v1')
      .setProject(process.env.APPWRITE_PROJECT_ID)
      .setKey(process.env.APPWRITE_API_KEY);

    this.account = new Account(this.client);
    this.databases = new Databases(this.client);
    this.storage = new Storage(this.client);

    this.databaseId = process.env.APPWRITE_DATABASE_ID;
    this.collections = {
      USERS: process.env.APPWRITE_COLLECTION_USERS || 'users',
      PRODUCTS: process.env.APPWRITE_COLLECTION_PRODUCTS || 'products',
      TICKETS: process.env.APPWRITE_COLLECTION_TICKETS || 'tickets',
      CATEGORIES: process.env.APPWRITE_COLLECTION_CATEGORIES || 'categories',
      PAYMENTS: process.env.APPWRITE_COLLECTION_PAYMENTS || 'payments',
      NOTIFICATIONS: process.env.APPWRITE_COLLECTION_NOTIFICATIONS || 'notifications',
      MESSAGES: process.env.APPWRITE_COLLECTION_MESSAGES || 'messages',
      ESCROW: process.env.APPWRITE_COLLECTION_ESCROW || 'escrow'
    };

    this.buckets = {
      PRODUCT_IMAGES: process.env.APPWRITE_BUCKET_PRODUCT_IMAGES || 'product-images',
      PROFILE_PICTURES: process.env.APPWRITE_BUCKET_PROFILE_PICTURES || 'profile-pictures'
    };
  }

  // Authentication methods
  async createUser(email, password, name) {
    try {
      const user = await this.account.create('unique()', email, password, name);
      logger.info(`User created: ${user.$id}`);
      return user;
    } catch (error) {
      logger.error('Failed to create user:', error);
      throw new Error(`Failed to create user: ${error.message}`);
    }
  }

  async createSession(email, password) {
    try {
      const session = await this.account.createEmailSession(email, password);
      logger.info(`Session created for user: ${session.userId}`);
      return session;
    } catch (error) {
      logger.error('Failed to create session:', error);
      throw new Error(`Failed to create session: ${error.message}`);
    }
  }

  // User operations
  async createUserProfile(userData) {
    try {
      return await this.databases.createDocument(
        this.databaseId,
        this.collections.USERS,
        userData.id || 'unique()',
        userData
      );
    } catch (error) {
      logger.error('Failed to create user profile:', error);
      throw new Error(`Failed to create user profile: ${error.message}`);
    }
  }

  async getUserProfile(userId) {
    try {
      return await this.databases.getDocument(
        this.databaseId,
        this.collections.USERS,
        userId
      );
    } catch (error) {
      logger.error('Failed to get user profile:', error);
      throw new Error(`Failed to get user profile: ${error.message}`);
    }
  }

  async updateUserProfile(userId, userData) {
    try {
      return await this.databases.updateDocument(
        this.databaseId,
        this.collections.USERS,
        userId,
        userData
      );
    } catch (error) {
      logger.error('Failed to update user profile:', error);
      throw new Error(`Failed to update user profile: ${error.message}`);
    }
  }

  // Product operations
  async createProduct(productData) {
    try {
      return await this.databases.createDocument(
        this.databaseId,
        this.collections.PRODUCTS,
        'unique()',
        {
          ...productData,
          created_at: new Date().toISOString(),
          tickets_sold: 0,
          status: 'active',
          raffle_drawn: false
        }
      );
    } catch (error) {
      logger.error('Failed to create product:', error);
      throw new Error(`Failed to create product: ${error.message}`);
    }
  }

  async getProducts(queries = []) {
    try {
      return await this.databases.listDocuments(
        this.databaseId,
        this.collections.PRODUCTS,
        queries
      );
    } catch (error) {
      logger.error('Failed to get products:', error);
      throw new Error(`Failed to get products: ${error.message}`);
    }
  }

  async getProduct(productId) {
    try {
      return await this.databases.getDocument(
        this.databaseId,
        this.collections.PRODUCTS,
        productId
      );
    } catch (error) {
      logger.error('Failed to get product:', error);
      throw new Error(`Failed to get product: ${error.message}`);
    }
  }

  async updateProduct(productId, productData) {
    try {
      return await this.databases.updateDocument(
        this.databaseId,
        this.collections.PRODUCTS,
        productId,
        {
          ...productData,
          updated_at: new Date().toISOString()
        }
      );
    } catch (error) {
      logger.error('Failed to update product:', error);
      throw new Error(`Failed to update product: ${error.message}`);
    }
  }

  // Ticket operations
  async getUserTickets(userId, queries = []) {
    try {
      const userQueries = [
        Query.equal('user_id', userId),
        ...queries
      ];
      return await this.databases.listDocuments(
        this.databaseId,
        this.collections.TICKETS,
        userQueries
      );
    } catch (error) {
      logger.error('Failed to get user tickets:', error);
      throw new Error(`Failed to get user tickets: ${error.message}`);
    }
  }

  async getProductTickets(productId, queries = []) {
    try {
      const productQueries = [
        Query.equal('product_id', productId),
        ...queries
      ];
      return await this.databases.listDocuments(
        this.databaseId,
        this.collections.TICKETS,
        productQueries
      );
    } catch (error) {
      logger.error('Failed to get product tickets:', error);
      throw new Error(`Failed to get product tickets: ${error.message}`);
    }
  }

  // Notification operations
  async createNotification(notificationData) {
    try {
      return await this.databases.createDocument(
        this.databaseId,
        this.collections.NOTIFICATIONS,
        'unique()',
        {
          ...notificationData,
          created_at: new Date().toISOString(),
          read: false
        }
      );
    } catch (error) {
      logger.error('Failed to create notification:', error);
      throw new Error(`Failed to create notification: ${error.message}`);
    }
  }

  async getUserNotifications(userId, queries = []) {
    try {
      const userQueries = [
        Query.equal('user_id', userId),
        Query.orderDesc('created_at'),
        ...queries
      ];
      return await this.databases.listDocuments(
        this.databaseId,
        this.collections.NOTIFICATIONS,
        userQueries
      );
    } catch (error) {
      logger.error('Failed to get user notifications:', error);
      throw new Error(`Failed to get user notifications: ${error.message}`);
    }
  }

  async markNotificationAsRead(notificationId) {
    try {
      return await this.databases.updateDocument(
        this.databaseId,
        this.collections.NOTIFICATIONS,
        notificationId,
        { read: true }
      );
    } catch (error) {
      logger.error('Failed to mark notification as read:', error);
      throw new Error(`Failed to mark notification as read: ${error.message}`);
    }
  }

  // File upload operations
  async uploadFile(file, bucketId) {
    try {
      return await this.storage.createFile(
        bucketId,
        'unique()',
        file
      );
    } catch (error) {
      logger.error('Failed to upload file:', error);
      throw new Error(`Failed to upload file: ${error.message}`);
    }
  }

  async getFileUrl(bucketId, fileId) {
    try {
      return this.storage.getFileView(bucketId, fileId);
    } catch (error) {
      logger.error('Failed to get file URL:', error);
      throw new Error(`Failed to get file URL: ${error.message}`);
    }
  }

  // Categories
  async getCategories() {
    try {
      return await this.databases.listDocuments(
        this.databaseId,
        this.collections.CATEGORIES,
        [Query.equal('active', true)]
      );
    } catch (error) {
      logger.error('Failed to get categories:', error);
      throw new Error(`Failed to get categories: ${error.message}`);
    }
  }
}

module.exports = new AppwriteService();
