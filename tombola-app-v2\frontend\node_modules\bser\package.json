{"_from": "bser@2.1.1", "_id": "bser@2.1.1", "_inBundle": false, "_integrity": "sha512-gQxTNE/GAfIIrmHLUE3oJyp5FO6HRBfhjnw4/wMmA63ZGDJnWBmgY/lyQBpnDUkGmAhbSe39tx2d/iTOAfglwQ==", "_location": "/bser", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "bser@2.1.1", "name": "bser", "escapedName": "bser", "rawSpec": "2.1.1", "saveSpec": null, "fetchSpec": "2.1.1"}, "_requiredBy": ["/fb-watchman"], "_resolved": "https://registry.npmjs.org/bser/-/bser-2.1.1.tgz", "_shasum": "e6787da20ece9d07998533cfd9de6f5c38f4bc05", "_spec": "bser@2.1.1", "_where": "C:\\Users\\<USER>\\OneDrive\\Documents\\Work\\dev\\tombola20\\tombola-app-v2\\frontend\\node_modules\\fb-watchman", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://wezfurlong.org"}, "bugs": {"url": "https://github.com/facebook/watchman/issues"}, "bundleDependencies": false, "dependencies": {"node-int64": "^0.4.0"}, "deprecated": false, "description": "JavaScript implementation of the BSER Binary Serialization", "directories": {"test": "test"}, "files": ["index.js"], "homepage": "https://facebook.github.io/watchman/docs/bser.html", "keywords": ["bser", "binary", "protocol"], "license": "Apache-2.0", "main": "index.js", "name": "bser", "repository": {"type": "git", "url": "git+https://github.com/facebook/watchman.git"}, "scripts": {"test": "node test/bser.js"}, "version": "2.1.1"}