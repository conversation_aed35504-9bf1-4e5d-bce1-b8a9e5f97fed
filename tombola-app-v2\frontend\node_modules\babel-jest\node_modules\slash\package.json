{"_from": "slash@^3.0.0", "_id": "slash@3.0.0", "_inBundle": false, "_integrity": "sha512-g9Q1haeby36OSStwb4ntCGGGaKsaVSjQ68fBxoQcutl5fS1vuY18H3wSt3jFyFtrkx+Kz0V1G85A4MyAdDMi2Q==", "_location": "/babel-jest/slash", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "slash@^3.0.0", "name": "slash", "escapedName": "slash", "rawSpec": "^3.0.0", "saveSpec": null, "fetchSpec": "^3.0.0"}, "_requiredBy": ["/babel-jest"], "_resolved": "https://registry.npmjs.org/slash/-/slash-3.0.0.tgz", "_shasum": "6539be870c165adbd5240220dbe361f1bc4d4634", "_spec": "slash@^3.0.0", "_where": "C:\\Users\\<USER>\\OneDrive\\Documents\\Work\\dev\\tombola20\\tombola-app-v2\\frontend\\node_modules\\babel-jest", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/slash/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Convert Windows backslash paths to slash paths", "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}, "engines": {"node": ">=8"}, "files": ["index.js", "index.d.ts"], "homepage": "https://github.com/sindresorhus/slash#readme", "keywords": ["path", "seperator", "slash", "backslash", "windows", "convert"], "license": "MIT", "name": "slash", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/slash.git"}, "scripts": {"test": "xo && ava && tsd"}, "version": "3.0.0"}