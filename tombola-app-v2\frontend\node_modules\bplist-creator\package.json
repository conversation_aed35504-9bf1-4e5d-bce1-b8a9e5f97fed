{"_from": "bplist-creator@0.1.0", "_id": "bplist-creator@0.1.0", "_inBundle": false, "_integrity": "sha512-sXaHZicyEEmY86WyueLTQesbeoH/mquvarJaQNbjuOQO+7gbFcDEWqKmcWA4cOTLzFlfgvkiVxolk1k5bBIpmg==", "_location": "/bplist-creator", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "bplist-creator@0.1.0", "name": "bplist-creator", "escapedName": "bplist-creator", "rawSpec": "0.1.0", "saveSpec": null, "fetchSpec": "0.1.0"}, "_requiredBy": ["/simple-plist"], "_resolved": "https://registry.npmjs.org/bplist-creator/-/bplist-creator-0.1.0.tgz", "_shasum": "018a2d1b587f769e379ef5519103730f8963ba1e", "_spec": "bplist-creator@0.1.0", "_where": "C:\\Users\\<USER>\\OneDrive\\Documents\\Work\\dev\\tombola20\\tombola-app-v2\\frontend\\node_modules\\simple-plist", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/nearinfinity/node-bplist-creator/issues"}, "bundleDependencies": false, "dependencies": {"stream-buffers": "2.2.x"}, "deprecated": false, "description": "Binary Mac OS X Plist (property list) creator.", "devDependencies": {"bplist-parser": "0.3.0", "is-buffer": "1.1.x", "nodeunit": "0.9.x"}, "homepage": "https://github.com/nearinfinity/node-bplist-creator#readme", "keywords": ["bplist", "plist", "creator"], "license": "MIT", "main": "bplistCreator.js", "name": "bplist-creator", "repository": {"type": "git", "url": "git+https://github.com/nearinfinity/node-bplist-creator.git"}, "scripts": {"test": "./node_modules/nodeunit/bin/nodeunit test"}, "version": "0.1.0"}