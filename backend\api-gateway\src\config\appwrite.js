const { Client, Account, Databases, Storage, Functions } = require('node-appwrite');

class AppwriteService {
  constructor() {
    this.client = new Client();
    this.client
      .setEndpoint(process.env.APPWRITE_ENDPOINT || 'http://localhost/v1')
      .setProject(process.env.APPWRITE_PROJECT_ID)
      .setKey(process.env.APPWRITE_API_KEY);

    this.account = new Account(this.client);
    this.databases = new Databases(this.client);
    this.storage = new Storage(this.client);
    this.functions = new Functions(this.client);

    this.databaseId = process.env.APPWRITE_DATABASE_ID;
    this.collections = {
      USERS: process.env.APPWRITE_COLLECTION_USERS,
      PRODUCTS: process.env.APPWRITE_COLLECTION_PRODUCTS,
      TICKETS: process.env.APPWRITE_COLLECTION_TICKETS,
      CATEGORIES: process.env.APPWRITE_COLLECTION_CATEGORIES,
      PAYMENTS: process.env.APPWRITE_COLLECTION_PAYMENTS,
      NOTIFICATIONS: process.env.APPWRITE_COLLECTION_NOTIFICATIONS,
      MESSAGES: process.env.APPWRITE_COLLECTION_MESSAGES
    };
  }

  // User operations
  async createUser(userData) {
    try {
      return await this.databases.createDocument(
        this.databaseId,
        this.collections.USERS,
        'unique()',
        userData
      );
    } catch (error) {
      throw new Error(`Failed to create user: ${error.message}`);
    }
  }

  async getUser(userId) {
    try {
      return await this.databases.getDocument(
        this.databaseId,
        this.collections.USERS,
        userId
      );
    } catch (error) {
      throw new Error(`Failed to get user: ${error.message}`);
    }
  }

  async updateUser(userId, userData) {
    try {
      return await this.databases.updateDocument(
        this.databaseId,
        this.collections.USERS,
        userId,
        userData
      );
    } catch (error) {
      throw new Error(`Failed to update user: ${error.message}`);
    }
  }

  // Product operations
  async createProduct(productData) {
    try {
      return await this.databases.createDocument(
        this.databaseId,
        this.collections.PRODUCTS,
        'unique()',
        productData
      );
    } catch (error) {
      throw new Error(`Failed to create product: ${error.message}`);
    }
  }

  async getProducts(queries = []) {
    try {
      return await this.databases.listDocuments(
        this.databaseId,
        this.collections.PRODUCTS,
        queries
      );
    } catch (error) {
      throw new Error(`Failed to get products: ${error.message}`);
    }
  }

  async getProduct(productId) {
    try {
      return await this.databases.getDocument(
        this.databaseId,
        this.collections.PRODUCTS,
        productId
      );
    } catch (error) {
      throw new Error(`Failed to get product: ${error.message}`);
    }
  }

  async updateProduct(productId, productData) {
    try {
      return await this.databases.updateDocument(
        this.databaseId,
        this.collections.PRODUCTS,
        productId,
        productData
      );
    } catch (error) {
      throw new Error(`Failed to update product: ${error.message}`);
    }
  }

  // Ticket operations
  async createTicket(ticketData) {
    try {
      return await this.databases.createDocument(
        this.databaseId,
        this.collections.TICKETS,
        'unique()',
        ticketData
      );
    } catch (error) {
      throw new Error(`Failed to create ticket: ${error.message}`);
    }
  }

  async getUserTickets(userId, queries = []) {
    try {
      const userQueries = [
        ...queries,
        `user_id=${userId}`
      ];
      return await this.databases.listDocuments(
        this.databaseId,
        this.collections.TICKETS,
        userQueries
      );
    } catch (error) {
      throw new Error(`Failed to get user tickets: ${error.message}`);
    }
  }

  async getProductTickets(productId, queries = []) {
    try {
      const productQueries = [
        ...queries,
        `product_id=${productId}`
      ];
      return await this.databases.listDocuments(
        this.databaseId,
        this.collections.TICKETS,
        productQueries
      );
    } catch (error) {
      throw new Error(`Failed to get product tickets: ${error.message}`);
    }
  }

  // File upload
  async uploadFile(file, bucketId) {
    try {
      return await this.storage.createFile(
        bucketId,
        'unique()',
        file
      );
    } catch (error) {
      throw new Error(`Failed to upload file: ${error.message}`);
    }
  }

  // Notification operations
  async createNotification(notificationData) {
    try {
      return await this.databases.createDocument(
        this.databaseId,
        this.collections.NOTIFICATIONS,
        'unique()',
        notificationData
      );
    } catch (error) {
      throw new Error(`Failed to create notification: ${error.message}`);
    }
  }
}

module.exports = new AppwriteService();
