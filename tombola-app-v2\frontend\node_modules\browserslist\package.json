{"_from": "browserslist@^4.24.0", "_id": "browserslist@4.25.0", "_inBundle": false, "_integrity": "sha512-PJ8gYKeS5e/whHBh8xrwYK+dAvEj7JXtz6uTucnMRB8OiGTsKccFekoRrjajPBHV8oOY+2tI4uxeceSimKwMFA==", "_location": "/browserslist", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "browserslist@^4.24.0", "name": "browserslist", "escapedName": "browserslist", "rawSpec": "^4.24.0", "saveSpec": null, "fetchSpec": "^4.24.0"}, "_requiredBy": ["/@babel/helper-compilation-targets", "/caniuse-api", "/core-js-compat", "/postcss-colormin", "/postcss-convert-values", "/postcss-merge-rules", "/postcss-minify-params", "/postcss-normalize-unicode", "/postcss-reduce-initial", "/stylehacks", "/webpack"], "_resolved": "https://registry.npmjs.org/browserslist/-/browserslist-4.25.0.tgz", "_shasum": "986aa9c6d87916885da2b50d8eb577ac8d133b2c", "_spec": "browserslist@^4.24.0", "_where": "C:\\Users\\<USER>\\OneDrive\\Documents\\Work\\dev\\tombola20\\tombola-app-v2\\frontend\\node_modules\\@babel\\helper-compilation-targets", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bin": {"browserslist": "cli.js"}, "browser": {"./node.js": "./browser.js", "path": false}, "bugs": {"url": "https://github.com/browserslist/browserslist/issues"}, "bundleDependencies": false, "dependencies": {"caniuse-lite": "^1.0.30001718", "electron-to-chromium": "^1.5.160", "node-releases": "^2.0.19", "update-browserslist-db": "^1.1.3"}, "deprecated": false, "description": "Share target browsers between different front-end tools, like Autoprefixer, Stylelint and babel-env-preset", "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "homepage": "https://github.com/browserslist/browserslist#readme", "keywords": ["caniuse", "browsers", "target"], "license": "MIT", "name": "browserslist", "repository": {"type": "git", "url": "git+https://github.com/browserslist/browserslist.git"}, "types": "./index.d.ts", "version": "4.25.0"}