{"_from": "semver@^6.3.1", "_id": "semver@6.3.1", "_inBundle": false, "_integrity": "sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==", "_location": "/babel-plugin-polyfill-corejs2/semver", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "semver@^6.3.1", "name": "semver", "escapedName": "semver", "rawSpec": "^6.3.1", "saveSpec": null, "fetchSpec": "^6.3.1"}, "_requiredBy": ["/babel-plugin-polyfill-corejs2"], "_resolved": "https://registry.npmjs.org/semver/-/semver-6.3.1.tgz", "_shasum": "556d2ef8689146e46dcea4bfdd095f3434dffcb4", "_spec": "semver@^6.3.1", "_where": "C:\\Users\\<USER>\\OneDrive\\Documents\\Work\\dev\\tombola20\\tombola-app-v2\\frontend\\node_modules\\babel-plugin-polyfill-corejs2", "author": {"name": "GitHub Inc."}, "bin": {"semver": "bin/semver.js"}, "bugs": {"url": "https://github.com/npm/node-semver/issues"}, "bundleDependencies": false, "deprecated": false, "description": "The semantic version parser used by npm.", "devDependencies": {"@npmcli/template-oss": "4.17.0", "tap": "^12.7.0"}, "files": ["bin", "range.bnf", "semver.js"], "homepage": "https://github.com/npm/node-semver#readme", "license": "ISC", "main": "semver.js", "name": "semver", "repository": {"type": "git", "url": "git+https://github.com/npm/node-semver.git"}, "scripts": {"lint": "echo linting disabled", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "posttest": "npm run lint", "snap": "tap test/ --100 --timeout=30", "template-oss-apply": "template-oss-apply --force", "test": "tap test/ --100 --timeout=30"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "content": "./scripts/template-oss", "version": "4.17.0"}, "version": "6.3.1"}