# Tombola V2 Docker Environment Configuration
# Copy this file to .env and update the values as needed

# Database Configuration
POSTGRES_DB=tombola_db
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_HOST=postgres
POSTGRES_PORT=5432
DATABASE_URL=********************************************/tombola_db

# API Configuration
NODE_ENV=development
API_PORT=3001
JWT_SECRET=your-super-secret-jwt-key-change-in-production
CORS_ORIGIN=http://localhost:3000

# Supabase Configuration (for compatibility)
SUPABASE_URL=http://localhost:5432
SUPABASE_ANON_KEY=your-anon-key-here
SUPABASE_SERVICE_KEY=your-service-key-here

# Frontend Configuration
EXPO_PUBLIC_API_URL=http://localhost:3001
EXPO_PUBLIC_WEB_PORT=3000

# Redis Configuration
REDIS_URL=redis://redis:6379

# Stripe Configuration (for payments)
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# Email Configuration (optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# File Upload Configuration
MAX_FILE_SIZE=10485760  # 10MB
UPLOAD_PATH=/app/uploads
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,webp

# Security Configuration
BCRYPT_ROUNDS=12
SESSION_SECRET=your-session-secret-change-in-production
RATE_LIMIT_WINDOW_MS=900000  # 15 minutes
RATE_LIMIT_MAX_REQUESTS=100

# Logging Configuration
LOG_LEVEL=debug
LOG_FORMAT=combined

# Development Configuration
HOT_RELOAD=true
DEBUG_MODE=true
