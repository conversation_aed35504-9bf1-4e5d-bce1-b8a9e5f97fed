# Tombola V2 Frontend Dockerfile
# React Native with Expo for web development

# Development stage
FROM node:20-alpine AS development

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apk add --no-cache \
    git \
    curl \
    bash

# Install Expo CLI globally
RUN npm install -g @expo/cli@latest

# Copy package files
COPY package*.json ./
COPY app.json ./
COPY expo.json ./
COPY tsconfig.json ./

# Install dependencies
RUN npm ci

# Copy source code
COPY src/ ./src/
COPY assets/ ./assets/
COPY App.tsx ./

# Create expo cache directory
RUN mkdir -p /app/.expo

# Expose ports
EXPOSE 3000 8081 8082

# Set environment variables
ENV EXPO_DEVTOOLS_LISTEN_ADDRESS=0.0.0.0
ENV EXPO_CLI_NO_INSTALL_WARNING=1
ENV EXPO_NO_TELEMETRY=1

# Development command
CMD ["npm", "run", "web"]

# Production build stage
FROM node:20-alpine AS build

WORKDIR /app

# Install system dependencies
RUN apk add --no-cache git

# Install Expo CLI
RUN npm install -g @expo/cli@latest

# Copy package files
COPY package*.json ./
COPY app.json ./
COPY expo.json ./
COPY tsconfig.json ./

# Install dependencies
RUN npm ci

# Copy source code
COPY src/ ./src/
COPY assets/ ./assets/
COPY App.tsx ./

# Build for web production
RUN npm run build:web

# Production stage with nginx
FROM nginx:alpine AS production

# Copy built files from build stage
COPY --from=build /app/dist /usr/share/nginx/html

# Copy nginx configuration
COPY nginx.conf /etc/nginx/conf.d/default.conf

# Expose port
EXPOSE 80

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost/ || exit 1

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
