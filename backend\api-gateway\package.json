{"name": "tombola-api-gateway", "version": "1.0.0", "description": "API Gateway for Tombola Raffle Application", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.0.0", "express-rate-limit": "^6.8.1", "http-proxy-middleware": "^2.0.6", "jsonwebtoken": "^9.0.1", "node-appwrite": "^11.0.0", "dotenv": "^16.3.1", "winston": "^3.10.0", "express-validator": "^7.0.1", "compression": "^1.7.4", "morgan": "^1.10.0", "redis": "^4.6.7", "bull": "^4.11.3"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.1", "supertest": "^6.3.3", "eslint": "^8.45.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.27.5"}, "engines": {"node": ">=18.0.0"}, "keywords": ["api-gateway", "microservices", "tombola", "raffle", "appwrite"], "author": "Tombola Team", "license": "MIT"}