{"_from": "call-bind@^1.0.8", "_id": "call-bind@1.0.8", "_inBundle": false, "_integrity": "sha512-oKlSFMcMwpUg2ednkhQ454wfWiU/ul3CkJe/PEHcTKuiX6RpbehUiFMXu13HalGZxfUwCQzZG747YXBn1im9ww==", "_location": "/call-bind", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "call-bind@^1.0.8", "name": "call-bind", "escapedName": "call-bind", "rawSpec": "^1.0.8", "saveSpec": null, "fetchSpec": "^1.0.8"}, "_requiredBy": ["/array-includes", "/array.prototype.findlast", "/array.prototype.findlastindex", "/array.prototype.flat", "/array.prototype.flatmap", "/array.prototype.tosorted", "/arraybuffer.prototype.slice", "/assert", "/es-abstract", "/es-iterator-helpers", "/function.prototype.name", "/is-array-buffer", "/is-nan", "/object-is", "/object.assign", "/object.entries", "/object.fromentries", "/object.groupby", "/object.values", "/reflect.getprototypeof", "/regexp.prototype.flags", "/safe-array-concat", "/string.prototype.matchall", "/string.prototype.trim", "/string.prototype.trimend", "/string.prototype.trimstart", "/typed-array-byte-length", "/typed-array-byte-offset", "/typed-array-length", "/typedarray.prototype.slice", "/which-typed-array"], "_resolved": "https://registry.npmjs.org/call-bind/-/call-bind-1.0.8.tgz", "_shasum": "0736a9660f537e3388826f440d5ec45f744eaa4c", "_spec": "call-bind@^1.0.8", "_where": "C:\\Users\\<USER>\\OneDrive\\Documents\\Work\\dev\\tombola20\\tombola-app-v2\\frontend\\node_modules\\typedarray.prototype.slice", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/ljharb/call-bind/issues"}, "bundleDependencies": false, "dependencies": {"call-bind-apply-helpers": "^1.0.0", "es-define-property": "^1.0.0", "get-intrinsic": "^1.2.4", "set-function-length": "^1.2.2"}, "deprecated": false, "description": "Robustly `.call.bind()` a function", "devDependencies": {"@ljharb/eslint-config": "^21.1.1", "auto-changelog": "^2.5.0", "encoding": "^0.1.13", "es-value-fixtures": "^1.5.0", "eslint": "=8.8.0", "evalmd": "^0.0.19", "for-each": "^0.3.3", "has-strict-mode": "^1.0.1", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "object-inspect": "^1.13.3", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0"}, "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./callBound": "./callBound.js", "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/ljharb/call-bind#readme", "keywords": ["javascript", "ecmascript", "es", "js", "callbind", "callbound", "call", "bind", "bound", "call-bind", "call-bound", "function", "es-abstract"], "license": "MIT", "main": "index.js", "name": "call-bind", "publishConfig": {"ignore": [".github/workflows"]}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/call-bind.git"}, "scripts": {"lint": "eslint --ext=.js,.mjs .", "postlint": "evalmd README.md", "posttest": "npx npm@'>=10.2' audit --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepack": "npmignore --auto --commentLines=auto", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "testling": {"files": "test/index.js"}, "version": "1.0.8"}