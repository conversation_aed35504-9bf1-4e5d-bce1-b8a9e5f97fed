{"_from": "babel-plugin-polyfill-regenerator@^0.6.1", "_id": "babel-plugin-polyfill-regenerator@0.6.4", "_inBundle": false, "_integrity": "sha512-7gD3pRadPrbjhjLyxebmx/WrFYcuSjZ0XbdUujQMZ/fcE9oeewk2U/7PCvez84UeuK3oSjmPZ0Ch0dlupQvGzw==", "_location": "/babel-plugin-polyfill-regenerator", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "babel-plugin-polyfill-regenerator@^0.6.1", "name": "babel-plugin-polyfill-regenerator", "escapedName": "babel-plugin-polyfill-regenerator", "rawSpec": "^0.6.1", "saveSpec": null, "fetchSpec": "^0.6.1"}, "_requiredBy": ["/@babel/plugin-transform-runtime", "/@babel/preset-env"], "_resolved": "https://registry.npmjs.org/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.6.4.tgz", "_shasum": "428c615d3c177292a22b4f93ed99e358d7906a9b", "_spec": "babel-plugin-polyfill-regenerator@^0.6.1", "_where": "C:\\Users\\<USER>\\OneDrive\\Documents\\Work\\dev\\tombola20\\tombola-app-v2\\frontend\\node_modules\\@babel\\preset-env", "bugs": {"url": "https://github.com/babel/babel-polyfills/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-define-polyfill-provider": "^0.6.4"}, "deprecated": false, "description": "A Babel plugin to inject imports to regenerator-runtime", "devDependencies": {"@babel/core": "^7.17.8", "@babel/helper-plugin-test-runner": "^7.16.7", "@babel/plugin-transform-regenerator": "~7.14.0"}, "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "gitHead": "d87c29c909148920ad18690b63d450c561842298", "homepage": "https://github.com/babel/babel-polyfills#readme", "keywords": ["babel-plugin"], "license": "MIT", "main": "lib/index.js", "name": "babel-plugin-polyfill-regenerator", "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel-polyfills.git", "directory": "packages/babel-plugin-polyfill-regenerator"}, "version": "0.6.4"}