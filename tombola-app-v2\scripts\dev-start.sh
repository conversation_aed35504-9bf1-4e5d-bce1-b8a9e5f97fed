#!/bin/bash

# Tombola V2 Development Startup Script
# This script starts the development environment with Docker Compose

set -e

echo "🚀 Starting Tombola V2 Development Environment..."

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if .env file exists, if not copy from .env.docker
if [ ! -f .env ]; then
    echo "📋 Creating .env file from .env.docker template..."
    cp .env.docker .env
    echo "⚠️  Please update the .env file with your actual configuration values."
fi

# Create necessary directories
echo "📁 Creating necessary directories..."
mkdir -p database/init
mkdir -p nginx/ssl
mkdir -p api-layer/uploads
mkdir -p frontend/dist

# Build and start services
echo "🔨 Building and starting Docker services..."
docker-compose up --build -d

# Wait for database to be ready
echo "⏳ Waiting for database to be ready..."
timeout=60
counter=0
while ! docker-compose exec -T postgres pg_isready -U postgres -d tombola_db > /dev/null 2>&1; do
    if [ $counter -eq $timeout ]; then
        echo "❌ Database failed to start within $timeout seconds"
        docker-compose logs postgres
        exit 1
    fi
    echo "Waiting for database... ($counter/$timeout)"
    sleep 1
    counter=$((counter + 1))
done

echo "✅ Database is ready!"

# Wait for API to be ready
echo "⏳ Waiting for API to be ready..."
timeout=60
counter=0
while ! curl -f http://localhost:3001/health > /dev/null 2>&1; do
    if [ $counter -eq $timeout ]; then
        echo "❌ API failed to start within $timeout seconds"
        docker-compose logs api
        exit 1
    fi
    echo "Waiting for API... ($counter/$timeout)"
    sleep 1
    counter=$((counter + 1))
done

echo "✅ API is ready!"

# Show service status
echo "📊 Service Status:"
docker-compose ps

echo ""
echo "🎉 Tombola V2 Development Environment is ready!"
echo ""
echo "📱 Frontend (React Native Web): http://localhost:3000"
echo "🔧 API (Fastify): http://localhost:3001"
echo "🗄️  Database (PostgreSQL): localhost:5432"
echo "🔴 Redis: localhost:6379"
echo "🌐 Nginx Proxy: http://localhost:80"
echo ""
echo "📋 Useful commands:"
echo "  View logs: docker-compose logs -f [service_name]"
echo "  Stop services: docker-compose down"
echo "  Restart service: docker-compose restart [service_name]"
echo "  Database shell: docker-compose exec postgres psql -U postgres -d tombola_db"
echo "  API shell: docker-compose exec api sh"
echo "  Frontend shell: docker-compose exec frontend sh"
echo ""
echo "🔧 To stop the environment, run: ./scripts/dev-stop.sh"
