{"_from": "caniuse-api@^3.0.0", "_id": "caniuse-api@3.0.0", "_inBundle": false, "_integrity": "sha512-bsTwuIg/BZZK/vreVTYYbSWoe2F+71P7K5QGEX+pT250DZbfU1MQ5prOKpPR+LL6uWKK3KMwMCAS74QB3Um1uw==", "_location": "/caniuse-api", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "caniuse-api@^3.0.0", "name": "caniuse-api", "escapedName": "caniuse-api", "rawSpec": "^3.0.0", "saveSpec": null, "fetchSpec": "^3.0.0"}, "_requiredBy": ["/postcss-colormin", "/postcss-merge-rules", "/postcss-reduce-initial"], "_resolved": "https://registry.npmjs.org/caniuse-api/-/caniuse-api-3.0.0.tgz", "_shasum": "5e4d90e2274961d46291997df599e3ed008ee4c0", "_spec": "caniuse-api@^3.0.0", "_where": "C:\\Users\\<USER>\\OneDrive\\Documents\\Work\\dev\\tombola20\\tombola-app-v2\\frontend\\node_modules\\postcss-colormin", "authors": ["n<PERSON><PERSON>", "MoOx"], "babel": {"presets": ["babel-preset-latest"]}, "bugs": {"url": "https://github.com/nyalab/caniuse-api/issues"}, "bundleDependencies": false, "dependencies": {"browserslist": "^4.0.0", "caniuse-lite": "^1.0.0", "lodash.memoize": "^4.1.2", "lodash.uniq": "^4.5.0"}, "deprecated": false, "description": "request the caniuse data to check browsers compatibilities", "devDependencies": {"babel-cli": "^6.22.2", "babel-eslint": "^5.0.0", "babel-preset-latest": "^6.22.0", "babel-tape-runner": "^2.0.1", "jshint": "^2.5.10", "npmpub": "^3.1.0", "tap-spec": "^4.1.1", "tape": "^4.6.0"}, "files": ["dist"], "homepage": "https://github.com/nyalab/caniuse-api#readme", "keywords": ["caniuse", "browserslist"], "license": "MIT", "main": "dist/index.js", "name": "caniuse-api", "repository": {"type": "git", "url": "git+https://github.com/nyalab/caniuse-api.git"}, "scripts": {"build": "babel src --out-dir dist", "lint": "jshint src", "prepublish": "npm run build", "release": "npmpub", "test": "npm run lint && babel-tape-runner test/*.js | tap-spec"}, "version": "3.0.0"}